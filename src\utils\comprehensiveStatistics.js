/*
 * @Description:
 */
// 综合统计相关接口

import http from './http'
const iemcApi = __PATH.VUE_APP_IEMC_API
const authwebApi = __PATH.VUE_APP_BASE_API
const alarmApi = __PATH.VUE_APP_WARN_API
const newIot = __PATH.VUE_TRANSFER_API
const newElevatorIot = __PATH.VUE_ELEVATOR_API

// ===================================================重点区域统计相关接口======================================================
// 报警排行
export function findAreaAlarmRank (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findAreaAlarmRank`, params)
}
// 人流排行
export function findAreaThroughRank (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findAreaThroughRank`, params)
}
export function findAreaThroughTrend (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findAreaThroughTrend`, params)
}

// ===============================================挂号统计=========================================================
// 挂号统计
export function departmentCount (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getDepartmentStatistics`, params)
}
// 挂号统计趋势
export function departmentRegisterTrend (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getDepartmentRegisterTrend`, params)
}
// 挂号渠道占比
export function sourcesAtioCount (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getSourcesAtioCount`, params)
}
// 号源管理
export function sourceManagement (params) {
  return http.postRequest(`${authwebApi}/comprehensive/getSourceManagement`, params)
}
// 就诊管理
export function encounterMgmt (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getEncounterMgmt`, params)
}
export function departmentRegister (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getDepartmentRegister`, params)
}
// 挂号记录
export function registerRecord (params) {
  return http.getRequest(`${authwebApi}/comprehensive/getRegisterRecord`, params)
}
// ==============================================================电梯运行统计=====================================================
// 电梯列表
export function elevatorList (params) {
  return http.getRequest(`${newIot}/assetsInfo/getMasterOrMonitoredByCode`, params)
}
// 当前电梯实时监测
export function currentElevatorInfo (id) {
  return http.postRequest(`${newIot}/assetsClient/assets/realTimeData?assetsId=${id}`)
}
// 电梯运行统计
export function elevatorRunCount (params) {
  return http.postRequest(`${newElevatorIot}/adsliftruncount/adsLiftRunCount/list`, params)
}
// 停靠楼层统计
export function adsLiftFloorCountList (params) {
  return http.postRequest(`${newElevatorIot}/adsliftfloorcount/adsLiftFloorCount/list`, params)
}

// ==============================================================报警统计=====================================================
// 报警总数同比环比
export function alarmStatistics (params) {
  return http.postRequest(`${alarmApi}/alarm/statistics/alarmStatistics`, params)
}
// 报警类型统计
export function alarmTypeStatistics (params) {
  return http.postRequest(`${alarmApi}/alarm/record/selectAlarmGroupByType`, params)
}

// 报警趋势
export function alarmTypeStatisticsTrend (params) {
  return http.postRequest(`${alarmApi}/alarm/statistics/alarmTypeStatisticsTrend`, params)
}

