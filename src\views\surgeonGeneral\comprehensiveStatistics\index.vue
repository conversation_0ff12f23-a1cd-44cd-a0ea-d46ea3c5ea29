<template>
  <div class="content">
    <div class="eahart-left">
      <AlarmStatistics v-if="isRefresh"/>
      <KeyAreasStatistics v-if="isRefresh"/>
    </div>

    <div class="eahart-right">
      <ElevatorRunStatistics />
      <div class="echarts-rightBottom">
        <RegistrationStatistics v-if="isRefresh"/>
        <ConsultationStatistics />
      </div>
    </div>
    <el-dialog

      v-dialogDrag
      :modal="false"
      :visible="alarmDetailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="() => alarmDetailVisible = false"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="alarmDetailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
  </div>
</template>
<script>
import AlarmStatistics from './components/alarmStatistics'
import KeyAreasStatistics from './components/keyAreasStatistics'
import ElevatorRunStatistics from './components/elevatorRunStatistics'

import RegistrationStatistics from './components/registrationStatistics'
import ConsultationStatistics from './components/consultationStatistics'
import  EmergencyDetail  from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  components: {
    AlarmStatistics,
    KeyAreasStatistics,
    ElevatorRunStatistics,
    RegistrationStatistics,
    ConsultationStatistics,
    EmergencyDetail
  },
  data() {
    return {
      alarmDetailVisible: false,
      currentAlarmId: '',
      isRefresh: true,
      timer: null
    }
  },
  mounted () {
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        let data = JSON.parse(event.data)
        console.log(data)
        if (data.type === 'elevatorAlarm') {
          this.currentAlarmId = data.alarmId
          this.alarmDetailVisible = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
        }
        // this.roomData = JSON.parse(JSON.parse(event.data).areaData)
      })
    } catch (errpr) { }
    if (this.timer) {
      clearInterval(this.timer)
      return
    }
    this.timer = setInterval(() => {
      this.isRefresh = false
      this.$nextTick((() => {
        this.isRefresh = true
      }))
    }, 1000 * 60 * 10)
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@import "~@/assets/sino-ui/common/var.scss";

.content {
  position: relative;
  background-color: #031553;
  // background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;

  justify-content: space-between;

  .eahart-left {
    width: calc(35% - 8px);
    height: 100%;
  }

  .eahart-right {
    width: calc(65% - 8px);
    height: 100%;
    display: flex;
    flex-direction: column;

    .echarts-rightBottom {
      width: 100%;
      height: calc(50% - 8px);

      margin-top: 16px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      & > div {
        background: url("~@/assets/images/bg-content1.png") no-repeat;
        background-size: 100% 100%;
        width: calc(50% - 8px);
      }
    }
  }
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
