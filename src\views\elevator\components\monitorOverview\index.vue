<template>
  <div class="viewContent">
    <ElevatorStatistics :roomData="roomData"/>
    <ElevatorList v-if="roomData.projectCode == 'DTXT'" :roomData="roomData"/>
    <EscalatorList v-else :roomData="roomData"/>
  </div>
</template>
<script>
export default {
  components: {
    // ElevatorStatistics: () => import('./components/elevatorStatistics'),
    ElevatorStatistics: () => import('./components/elevatorStatisticsNew'),
    ElevatorList: () => import('./components/elevatorList'),
    EscalatorList: () => import('./components/escalatorList')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  mounted() {
    console.log('===', this.roomData)
  }

}
</script>
<style lang="scss" scoped>
.viewContent{
  width: 100%;
  height: 100%;

}
</style>
