
import http from './http'

const newIotApi = __PATH.VUE_TRANSFER_API
const elevarotApi = __PATH.VUE_ELEVATOR_API
const iemcApi = __PATH.VUE_APP_IEMC_API

// ===========================================================新监测电梯================================================
// 电梯统计
export function elevatorStaticsist (params) {
  return http.getRequest(`${newIotApi}/elevator/elevatorStatistics`, {
    ...params
  })
}

// ================================================电梯系统===========================================================
// 开门次数排行
export function doorOpenedCntList (params) {
  return http.postRequest(`${elevarotApi}/adsliftruncount/adsLiftRunCount/doorOpenedCntList`, {
    ...params
  })
}
// 运行楼层排行
export function floorCountList (params) {
  return http.postRequest(`${elevarotApi}/adsliftfloorcount/adsLiftFloorCount/FloorCountList`, {
    ...params
  })
}

// 开门分析
export function openDooraAalysis (params) {
  return http.postRequest(`${elevarotApi}/adsliftruncount/adsLiftRunCount/doorOpenedCntCount`, {
    ...params
  })
}
// 开门时间统计
export function openDoorTimeStatistics (params) {
  return http.postRequest(`${elevarotApi}/adsliftruncount/adsLiftRunCount/doorOpenedTimeCount`, {
    ...params
  })
}
// 开门占比
export function doorOpenedRatio (params) {
  return http.postRequest(`${elevarotApi}/adsliftruncount/adsLiftRunCount/doorOpenedRatio`, {
    ...params
  })
}

// ==============================================================停靠分析===========================================
// 电梯停靠统计
export function elevatorStopCount (params) {
  return http.postRequest(`${elevarotApi}/adsliftfloorcount/adsLiftFloorCount/list`, {
    ...params
  })
}
export function elevatorStopFloorCount (params) {
  return http.postRequest(`${elevarotApi}/adsliftfloorcount/adsLiftFloorCount/liftStopCountList`, {
    ...params
  })
}

