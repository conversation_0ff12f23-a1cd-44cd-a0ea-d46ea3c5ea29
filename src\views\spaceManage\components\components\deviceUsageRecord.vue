<template>
  <div class="deviceTravelRecord">
    <div class="date-type">
      <p v-for="item in typeList" :key="item.value" class="type-item" :class="{'active-type': activeType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
    </div>
    <div class="search-box">
      <el-input v-if="activeType == 1" v-model="queryParam.searchKeyword" placeholder="名称/编码" clearable @input="nameSearchInput()"></el-input>
      <el-input v-else v-model="queryParam.searchKeyword" placeholder="名称/编码" clearable @input="nameSearchInput()"></el-input>
      <el-select v-if="activeType == 2" v-model="queryParam.registType" filterable clearable placeholder="全部挂号方式" @change="tableList">
        <el-option label="现场" value="0"></el-option>
        <el-option label="网约" value="1"></el-option>
      </el-select>
      <el-date-picker v-model="queryParam.visitDate" type="date" placeholder="选择日期" popper-class="date-style" :clearable="false" value-format="yyyy-MM-dd" @change="dateChange"/>
      <el-time-select
        v-show="activeType != 1"
        v-model="queryParam.startTime"
        popper-class="date-style"
        placeholder="开始时间"
        :clearable="false"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00'
        }"
        @change="dateChange"
      />
      <el-time-select
        v-show="activeType != 1"
        v-model="queryParam.endTime"
        popper-class="date-style"
        placeholder="结束时间"
        :clearable="false"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00',
          minTime: queryParam.startTime
        }"
        @change="dateChange"
      />
    </div>
    <div v-if="activeType == 2" class="count-list">
      <div v-for="(item, index) in countList" :key="index" class="count-list-item" style="width: calc(100% / 3 - 10px)">
        <p class="item-name">{{ item.name }}</p>
        <p class="item-value" :style="{color: item.color}">{{ usageData[item.key] | formatSeconds }}</p>
      </div>
    </div>
    <div style="flex: 1; overflow: hidden;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column v-for="(item, i) in getColumn" :key="i" :prop="item.prop" :label="item.name" :width="item.width" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="item.prop == 'visitBeginTime'">
              {{ (scope.row['visitDate'] || '') }}  {{ scope.row['visitBeginTime'] }} - {{ scope.row['visitEndTime'] }}
            </span>
            <span v-else-if="item.prop == 'registType'">
              {{ scope.row['registType'] == 0 ? '现场' : '网约' }}
            </span>
            <span v-else-if="item.prop == 'visitStatus'">
              {{ scope.row['visitStatus'] == 0 ? '复诊' : '初诊' }}
            </span>
            <div v-else>
              {{ scope.row[scope.column.property] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
import { GetDoctorVisitRecord, GetPatientVisitRecord, GetChairUseStat } from '@/utils/spaceManage'
import utils from '@/assets/common/utils'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceTravelRecord',
  filters: {
    formatSeconds(seconds) {
      if (!seconds) return '-'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    }
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeType: 1,
      typeList: [
        { name: '出诊记录', value: 1 },
        { name: '就诊记录', value: 2 }
      ],
      tableLoading: false,
      tableData: [],
      queryParam: {
        searchKeyword: '',
        visitDate: moment().format('YYYY-MM-DD'),
        startTime: '00:00',
        endTime: '24:00',
        registType: '' // 挂号方式
      },
      currentPage: 1,
      pageSize: 15,
      total: 0,
      countList: [
        { name: '空闲时长', key: 'freeCount', color: '#61E29D' },
        { name: '正常使用时长', key: 'normalCount', color: '' },
        { name: '异常占用时长', key: 'exceptionalCount', color: '#FF2D55' }
      ],
      usageData: {}
    }
  },
  computed: {
    getColumn() {
      if (this.activeType == 1) {
        return [
          { name: '医生名称', prop: 'doctorName' },
          { name: '岗位', prop: 'postName' },
          { name: '所在科室', prop: 'departmentName' },
          { name: '出诊椅位', prop: 'chairName' },
          { name: '出诊日期', prop: 'visitBeginTime', width: 240 }
        ]
      } else {
        return [
          { name: '病历编码', prop: 'caseCode' },
          { name: '患者名称', prop: 'patientName' },
          { name: '挂号方式', prop: 'registType' },
          { name: '初复诊', prop: 'visitStatus' },
          { name: '主治医生', prop: 'doctorName' },
          { name: '就诊椅位', prop: 'chairName' },
          { name: '就诊时间', prop: 'visitBeginTime', width: 340 }
        ]
      }
    }
  },
  created() {
    this.tableList()
  },
  methods: {
    dateChange(val) {
      if (this.activeType != 1) {
        this.getChairUseStat()
      }
      this.tableList()
    },
    activeTabEvent(val) {
      Object.assign(this.$data.queryParam, this.$options.data().queryParam)
      this.activeType = val
      if (val != 1) {
        this.getChairUseStat()
      }
      this.tableList()
    },
    // 获取椅位使用统计
    getChairUseStat() {
      let {visitDate, startTime, endTime} = this.queryParam
      let params = {
        monitorDeviceId: this.deviceId,
        dateType: 'day',
        sTime: `${visitDate} ${startTime}:00`,
        eTime: `${visitDate} ${endTime}:00`
      }
      GetChairUseStat(params).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.usageData = res.data.result
        }
      })
    },
    nameSearchInput: utils.throttle(function () {
      this.tableList()
    }, 1000),
    // 出诊记录 / 就诊记录
    tableList() {
      let {searchKeyword, visitDate, registType, startTime, endTime} = this.queryParam
      let params = this.activeType == 1 ? {
        chairCode: this.deviceId,
        page: this.currentPage,
        pageSize: this.pageSize,
        searchKeyword,
        visitDate
      } : {
        chairCode: this.deviceId,
        page: this.currentPage,
        pageSize: this.pageSize,
        searchKeyword,
        beginTime: `${visitDate} ${startTime}:00`,
        endTime: `${visitDate} ${endTime == '24:00' ? '23:59:59' : endTime + ':00'}`,
        registType
      }
      this.tableLoading = true
      this.tableData = []
      let dataFun = this.activeType == 1 ? GetDoctorVisitRecord : GetPatientVisitRecord
      dataFun(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.tableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.tableList()
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceTravelRecord {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .date-type {
    padding-top: 8px;
    margin-left: 15px;
    display: flex;
    justify-content: center;
    .type-item {
      cursor: pointer;
      padding: 6px 12px;
      font-weight: 400;
      font-size: 14px;
      color: #B0E3FA;
      margin-right: 8px;
      border: 1px solid transparent;
      background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
    }
    .active-type {
      color: #8BDDF5;
      background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
    }
  }
  ::v-deep(.search-box) {
    padding: 16px 0px 16px 0px;
    display: flex;
    align-items: center;
    // .el-select {
    //   margin-right: 10px;
    // }
    .el-input {
      margin-right: 10px;
      width: 250px;
      .el-input__inner {
        height: 35px;
        border: 1px solid rgba(133, 145, 206, 0.5);
        border-radius: 4px;
      }
      .el-input__icon {
        line-height: 35px;
      }
      .el-input__prefix {

      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: transparent;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      height: 35px;
      line-height: 35px;
    }
    .el-range-input {
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .count-list {
    padding-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    .count-list-item {
      width: calc(25%);
      // width: calc(25% - 7.5px);
      padding: 10px 0px;
      flex-shrink: 0;
      // margin-right: 10px;
      text-align: center;
      background: #8591ce0d;
      .item-name{
        color: #fff;
        font-size: 15px;
      }
      .item-value{
        color: #D6EFF1;
        font-size: 24px;
        margin-top: 8px;
        word-break: break-all;
      }
    }
    .count-list-item:nth-child(4n){
      margin-right: 0px;
    }
  }
}
</style>
