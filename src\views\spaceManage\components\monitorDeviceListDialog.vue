<template>
  <dialogFrame
    :visible="isDialog"
    :breadcrumb="breadcrumb"
    :title="title"
    @back="back"
    @update:visible="closeDialogFrame"
  >
    <component :is="activeComponent" :roomData="roomData" :seleteItem="seleteItem" :keyAreasSpaceId="keyAreasSpaceId" :deviceIds="deviceIds" :isSpecial="isSpecial" :webSocketObj="webSocketObj" @openDetailComponent="openDetailComponent"></component>
  </dialogFrame>
</template>

<script>
export default {
  name: 'monitorDeviceListDialog',
  components: {
    dialogFrame: () => import('@/components/common/DialogFrame'),
    monitorDeviceList: () => import('./components/monitorDeviceList.vue')
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 重点区域空间id
    keyAreasSpaceId: {
      type: Array,
      default: () => []
    },
    // 设备id列表
    deviceIds: {
      type: Array,
      default: () => []
    },
    // 是否有特殊处理
    isSpecial: {
      type: Boolean,
      default: false
    },
    webSocketObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      breadcrumb: [{ label: '设备列表', name: 'monitorDeviceList' }],
      activeComponent: 'monitorDeviceList',
      title: '设备列表',
      seleteItem: {}
    }
  },
  computed: {
  },
  created () {
    this.$tools.showDialog()
    // try {
    //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    // } catch (error) {}
  },
  destroyed() {
    this.$tools.showDialog()
    // try {
    //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    // } catch (error) {}
  },
  methods: {
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params.key
      this.seleteItem = params.data
      let obj = {
        deviceDetails: { label: '设备详情', name: 'deviceDetails' }
      }
      this.breadcrumb.push(obj[params.key])
      this.title = obj[params.key].label
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.$emit('close', false)
      this.activeComponent = 'monitorDeviceList'
      this.title = '设备列表'
      this.breadcrumb = [{ label: '设备列表', name: 'monitorDeviceList' }]
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name
      let arr = []
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i])
        if (this.breadcrumb[i].name == name) {
          break
        }
      }
      this.breadcrumb = arr
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label
      }
    }
  }
}

</script>

<style lang="scss" scoped>

</style>
