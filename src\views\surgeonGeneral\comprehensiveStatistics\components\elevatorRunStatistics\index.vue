<template>
  <div class="echarts-rightTop"  @mouseenter="clearAutoSwitch" @mouseleave="startAutoSwitch">
    <BgTitle showMore @moreClick="showDetail">
      <template #title>
        <div>
          <span style="margin-right: 10px">电梯运行统计</span>
          <el-dropdown ref="dropdownRef" trigger="click" @command="elevatorChange"  @visible-change="handleDropdownVisibleChange">
            <span class="el-dropdown-link" style="color: #b0e3fa">
              {{ elevatorList.find((v) => v.id == elevator)?.assetsName ?? "" }}
              <i class="el-icon-caret-bottom"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <!-- 添加搜索框 -->
              <el-input
                v-model="elevatorSearchQuery"
                placeholder="搜索电梯"
                size="small"
                style="margin-bottom: 10px;"
              />
              <!-- 过滤后的列表 -->
              <div style="max-height: 300px; overflow-y: auto;">
                <el-dropdown-item
                  v-for="item in filteredElevatorList"
                  :key="item.id"
                  :command="item.id"
                  :class="{ isBjxl: elevator == item.id }"
                >
                  {{ item.assetsName }}
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </template>
      <template #right>
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ dataTypeList.find((v) => v.value == dateType)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dataTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: dateType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </BgTitle>
    <div class="bg-content">
      <div style="width: calc(50% - 4px); height: 100%;">
        <LeftTopContent  :elevatorId="elevator" :date="dateType" @showDetail="showDetail"/>
        <LeftBottomContent :elevatorId="elevator" :date="dateType" @itemClick="showDetail"/>
      </div>
      <RightBottomContent :elevatorId="elevator" :date="dateType"/>

    </div>
    <OpenDoorRecord v-if="visible" :dialogShow="visible" :roomData="{}"  :selectData="currentItem" @openDoorRecordClose="visible = false" />
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import LeftTopContent from './leftTopContent.vue'
import LeftBottomContent from './leftBottomContent.vue'
import RightBottomContent from './rightBottomContent.vue'
import OpenDoorRecord from '@/views/elevator/components/openDoorAnalysis/openDoorRecord.vue'
import { elevatorList } from '@/utils/comprehensiveStatistics'
import dayjs from 'dayjs'
export default {
  components: {
    BgTitle,
    LeftTopContent,
    LeftBottomContent,
    RightBottomContent,
    OpenDoorRecord
  },
  data() {
    return {
      dateType: 'day',
      dataTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' }
      ],
      elevator: '',
      elevatorList: [],
      visible: false,
      currentItem: {},
      intervalId: null,
      elevatorSearchQuery: ''
    }
  },
  computed: {
    filteredElevatorList() {
      const query = this.elevatorSearchQuery.trim().toLowerCase()
      if (!query) return this.elevatorList
      return this.elevatorList.filter(item =>
      item.assetsName?.toLowerCase().includes(query)
      )
    }
  },

  mounted () {
    this.getElevatorList()
    this.startAutoSwitch()
  },
  beforeDestroy() {
    this.clearAutoSwitch()
  },
  methods: {
    handleDropdownVisibleChange(visible) {
      if (!visible) {
        this.elevatorSearchQuery = '' // 关闭时清空搜索框
      }
    },
    elevatorChange (val) {
      this.elevator = val
    },
    // 时间类型切换
    dataTypeCommand(val) {
      this.dateType = val
    },
    initDate (val) {
      let arr = []
      switch (val) {
        case 'day':
          arr = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
          break
        case 'week':
          arr = [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')]
          break
        case 'month':
          arr = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
          break
        case 'year':
          arr = [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')]
          break
      }
      return arr
    },
    showDetail (data) {
      this.visible = true
      this.currentItem = {
        ...data,
        elevatorId: this.elevatorId,
        date: this.dateType,
        dateRange: this.initDate(this.dateType)
      }
      this.clearAutoSwitch()
    },
    handleClose () {
      this.visible = false
      this.startAutoSwitch()
    },
    getElevatorList () {
      let params = {
        sysOfCode: 'DTXT'
      }
      elevatorList(params).then(res => {
        if (res.data.code == 200) {
          this.elevatorList = res.data.data
          this.elevator = this.elevatorList[0].id
        }
      })
    },
    startAutoSwitch() {
      if (!this.intervalId) {
        this.intervalId = setInterval(() => {
          const currentIndex = this.elevatorList.findIndex(v => v.id === this.elevator)
          const nextIndex = (currentIndex + 1) % this.elevatorList.length
          this.elevator = this.elevatorList[nextIndex].id
        }, 20000)
      }
    },
    clearAutoSwitch() {
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.echarts-rightTop {
  height: calc(50% - 8px);
  background: url("~@/assets/images/bg-content2.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px;
    width: 100%;
    height: calc(100% - 44px);
    display: flex;
    flex-wrap: wrap;
  }
  .dropdown-title{
    color: #fff;
    cursor: pointer;
  }
}
.statistics_item {
  width: calc(100% - 4px);
  height: calc(50% - 4px);
  // border: 1px solid #fff;
  margin-right: 8px;
  &:nth-child(even) {
    margin-right: 0;
  }
}

.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
:deep(.el-dropdown .el-popper .el-dropdown-menu) {
  max-height: 400px;
  overflow-y: auto;
}
</style>
