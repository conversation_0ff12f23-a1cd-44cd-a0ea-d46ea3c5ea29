<template>
  <div style="height: 100%">
    <ModuleCard title="报警统计" style="height: 30%">
      <div slot="title-right" class="title-right">
        <el-dropdown trigger="click" @command="dateChange">
          <span class="el-dropdown-link"> {{ dateList.find((v) => v.value == dateType)?.name ?? '' }} <i class="el-icon-arrow-down"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in dateList" :key="item.value" :command="item.value" :class="{ isBjxl: dateType == item.value }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <img src="@/assets/images/order_more.png" width="24" height="24" @click="showAlarmDetail()">
      </div>
      <div id="alarmStatiscist" slot="content">
      </div>

    </ModuleCard>
    <ModuleCard title="电梯报警排名" style="height: 40%">
      <div slot="title-right">
        <el-dropdown trigger="click" @command="alarmTypeChange">
          <span class="el-dropdown-link"> {{ alartTypeList.find((v) => v.alarmTypeId == alarmType)?.alarmTypeName ?? '' }} <i class="el-icon-arrow-down"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in alartTypeList" :key="item.alarmTypeId" :command="item.alarmTypeId" :class="{ isBjxl: alarmType == item.alarmTypeId }">{{ item.alarmTypeName }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div id="elevatorAlarmBar" slot="content">
      </div>
    </ModuleCard>
    <ModuleCard title="报警排序" style="height: 30%">
      <div slot="content" style="height: 100%;">
        <el-table
          :data="alarmDataList"
          class="table-center-transfer"
          height="100%"
          :cell-style="$tools.setCell(4)"
          :header-cell-style="$tools.setHeaderCell(4)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="rowClick"
        >
          <el-table-column prop="surveyName" label="设备名称" >
          </el-table-column>
          <el-table-column prop="count" label="报警次数" >
          </el-table-column>
        </el-table>
      </div>
    </ModuleCard>
    <AlarmDetailList v-if="visible" :visible="visible" :params="currentItem" @showDetail="showDetail" @close="closeDialog"></AlarmDetailList>
    <el-dialog

      v-dialogDrag
      :modal="false"
      :visible="detailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDetail"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="detailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { getAlarmStatisticPie, alarmTypeData, getElevatorFaultData } from '@/utils/elevatorApi'
import dayjs from 'dayjs'
import AlarmDetailList from '@/views/surgeonGeneral/comprehensiveStatistics/components/alarmStatistics/detail.vue'
import EmergencyDetail from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  components: {
    AlarmDetailList,
    EmergencyDetail
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dateType: 'day',
      dateList: [
        { name: '今日', value: 'day' },
        { name: '本周', value: 'week' },
        { name: '本月', value: 'month' },
        { name: '本年', value: 'year' }
      ],
      pieChart: null,
      lineChart: null,
      alarmType: '',
      alartTypeList: [],
      alarmDataList: [],
      visible: false,
      currentItem: {},
      detailVisible: false,
      currentAlarmId: ''
    }
  },
  mounted () {
    this.getAlarmTypeList()
    this.init()
  },
  methods: {
    init () {
      this.getRightCenter()
      this.getElevatorFaultData()
    },
    getAlarmTypeList () {
      alarmTypeData({projectCode: this.roomData.projectCode}).then(res => {
        if (res.data.code == 200) {
          this.alartTypeList = res.data.data
          this.alartTypeList.unshift({
            alarmTypeId: '',
            alarmTypeName: '全部报警类型'
          })
        }
      })
    },
    initDate (val) {
      let arr = []
      switch (val) {
        case 'day':
          arr = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
          break
        case 'week':
          arr = [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')]
          break
        case 'month':
          arr = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
          break
        case 'year':
          arr = [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')]
          break
      }
      return arr
    },
    dateChange (val) {
      this.dateType = val
      this.init()
      this.$emit('changeDateType', { typeName: val })
    },
    // 获取报警类型占比
    getRightCenter() {
      const params = {
        startTime: this.initDate(this.dateType)[0],
        endTime: this.initDate(this.dateType)[1],
        projectCode: this.roomData.projectCode
      }
      getAlarmStatisticPie(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.initPieChart(data.data)
        }
      })
    },
    initPieChart (valueList) {
      if (this.pieChart) {
        this.pieChart.dispose()
      }
      let dom = document.getElementById('alarmStatiscist')
      this.pieChart = echarts.init(dom)
      this.pieChart.resize()
      let option = {}
      if (valueList && valueList.length !== 0) {

        var scaleData = valueList
        var xdata = valueList.map((item) => {
          return item.name || ''
        })
        var data = []
        var colorList = ['#E88D6B', '#61E29D', '#5E89EE', '#0A84FF', '#F4D982']
        for (var i = 0; i < scaleData.length; i++) {
          data.push({
            value: scaleData[i].value,
            name: scaleData[i].name,
            parameterId: scaleData[i].parameterId,
            itemStyle: {
              borderWidth: 2,
              shadowBlur: 200,
              borderColor: '#0A1732',
              color: colorList[i],
              label: {
                show: true
              }
            }
          })
        }
        var seriesObj = [
          {
            name: '',
            type: 'pie',
            clockwise: false,
            center: ['20%', '50%'],
            radius: ['45%', '60%'], // 调整饼图的大小
            label: {
              show: false,
              position: 'center',
              formatter: '{c_style|{c}件}\n{b_style|{b}}',
              rich: {
                b_style: {
                  fontSize: 12,
                  fontWeight: 400,
                  color: '#fff'
                },
                c_style: {
                  padding: [0, 0, 10, 0],
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#fff'
                }
              }
            },
            emphasis: {
              scale: true,
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'normal'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          },
          {
            type: 'pie',
            radius: ['56%', '60%'],
            center: ['20%', '50%'],
            animation: false,
            emphasis: {
              scale: false
            },
            data: [
              {
                value: 100
              }
            ],
            label: {
              show: false
            },
            itemStyle: {
              color: 'rgba(133,145,206,0.15)'
            }
          },
          // 外边框
          {
            name: '外边框',
            type: 'pie',
            clockwise: false, // 顺时加载
            emphasis: {
              scale: false
            },
            center: ['20%', '50%'],
            radius: ['55%', '70%'],
            label: {
              show: false
            },
            data: [
              {
                value: 0,
                name: '',
                itemStyle: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            ]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: '50%',
            animation: false,
            emphasis: {
              scale: false
            },
            center: ['20%', '50%'],
            itemStyle: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            },
            data: [
              {
                value: 100
              }
            ]
          }
        ]
        option = {
          backgroundColor: 'rgba(128, 128, 128, 0)',
          legend: {
            selectedMode: false,
            orient: 'vertical',
            type: 'scroll',
            x: 'left',
            top: '6%',
            left: '45%',
            bottom: '0%',
            data: xdata,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 13,
            pageTextStyle: {
              color: '#fff'
            },
            pageIconColor: '#fff', // 设置翻页按钮的激活颜色
            pageIconInactiveColor: '#606266', // 设置翻页按钮的非激活颜色
            textStyle: {
              fontSize: 12, // 字体大小
              color: '#B3C2DD' //  字体颜色
            },
            formatter: function (name) {
              var oa = option.series[0].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[0].data.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + oa[i].name + ' ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
                }
              }
            },
            tooltip: {
              show: false
            }
          },

          series: seriesObj
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.pieChart.clear()
      this.pieChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.pieChart.resize()
      })
      clearInterval(this.highlightTimer)
      this.highlightTimer = null
      this.currentIndex = -1
      this.highlightTimer = setInterval(() => {
        this.startTooltipLoop(this.pieChart, valueList.length)
      }, 1000)
      // 鼠标移入暂停
      this.pieChart.on('mouseover', (params) => {
        this.pieChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentIndex
        })
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      })
      // 鼠标移出继续
      this.pieChart.on('mouseout', (params) => {
        if (this.highlightTimer) return
        this.highlightTimer = setInterval(() => {
          this.startTooltipLoop(this.pieChart, valueList.length)
        }, 1000)
      })
      // 先移除点击事件 解决点击事件重复绑定
      this.pieChart.off('click')
      // 图例点击事件
      this.pieChart.on('click', (params) => {
        this.pieChart.setOption({
          legend: { selected: { [params.name]: true } } // 取消点击图例置灰
        })
        console.log(data[params.dataIndex])
        let obj = {
          id: data[params.dataIndex].parameterId,
          projectCode: this.roomData.projectCode
        }
        this.showAlarmDetail(obj)
      })
    },
    startTooltipLoop(getchart, length) {
      // 取消之前高亮的图形
      getchart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      this.currentIndex = (this.currentIndex + 1) % length
      // 高亮当前图形
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      // 显示tooltip
      // getchart.dispatchAction({
      //   type: 'showTip',
      //   seriesIndex: 0,
      //   dataIndex: this.currentIndex
      // })
    },

    getElevatorFaultData () {
      const data = {
        projectCode: this.roomData.projectCode,
        alarmType: this.alarmType,
        startTime: this.initDate(this.dateType)[0],
        endTime: this.initDate(this.dateType)[1]
      }
      getElevatorFaultData(data).then(res => {
        if (res.data.code === '200') {
          this.alarmDataList = res.data.data
          this.initLineChart(res.data.data)
        }
      })
    },
    alarmTypeChange (val) {
      this.alarmType = val
      this.getElevatorFaultData()
    },
    initLineChart (data) {
      if (this.lineChart) {
        this.lineChart.dispose()
      }
      let dom = document.getElementById('elevatorAlarmBar')
      this.lineChart = echarts.init(dom)
      let option = {}
      if (data && data.length) {
        option = {
          yAxis: [
            {
              type: 'category',
              position: 'left', // 左侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: false // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map((item) => item.surveyName)
            },
            {
              type: 'category',
              position: 'right', // 右侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: false // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map((el) => {
                return {
                  value: el.count
                }
              })
            }
          ],
          xAxis: {
            type: 'value'
          },
          grid: {
            left: '2%',
            right: '5%',
            top: '10%',
            bottom: '5%',
            containLabel: true
          },
          series: [
            {
              data: data.map((el) => {
                return {
                  value: el.count
                }
              }),
              type: 'bar',
              barWidth: 10,
              barGap: '10%',
              itemStyle: {
                color: '#8BDDF5'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.lineChart.resize()
      })
      this.lineChart.setOption(option)
      // 添加柱状图鼠标点击事件
      this.lineChart.on('click', (params) => {
        const clickedIndex = params.dataIndex
        // 修改柱状图颜色
        option.series[0].data.forEach((el) => {
          el.itemStyle = null
        })
        option.series[0].data[clickedIndex].itemStyle = {
          color: '#FFCA64' // 修改为指定颜色
        }
        console.log(option.series[0].data[clickedIndex])
        this.lineChart.setOption(option) // 更新图表配置
        let obj = {
          ...option.series[0].data[clickedIndex],
          type: this.alarmType
        }
        this.showAlarmDetail(obj)
      })
    },
    rowClick (row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ObtainElevatorCode(row.alarmObjectId)
      } catch (error) {}
    },
    showAlarmDetail (data) {
      this.currentItem = {
        ...data,
        projectCode: this.roomData.projectCode
      }
      this.visible = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(true)
      } catch (error) {}
    },
    closeDialog () {
      this.visible = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(false)
      } catch (error) {}
    },
    showDetail (row) {
      this.currentAlarmId = row.alarmId
      this.detailVisible = true
    },
    closeDetail () {
      this.detailVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
#alarmStatiscist, #elevatorAlarmBar{
  width: 100%;
  height: 100%;
}
:deep(.el-table__body-wrapper) {
  height: calc(100% - 48px);
}
.title-right{
  display: flex;
  align-items: center;
}
::v-deep(.el-dropdown-link) {
  color: #fff;
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
