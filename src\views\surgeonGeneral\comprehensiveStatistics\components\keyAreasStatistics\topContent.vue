<template>
  <div class="statistics_item first" style="margin-bottom: 8px">
    <div class="statistics_current">
      <div class="item_title">区域人流排行</div>
      <div id="areaBar" class="item_content"></div>
    </div>
    <div class="statistics_current">
      <div class="item_title">区域报警排行</div>
      <div id="areaAlarmBar" class="item_content"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import {
  findAreaAlarmRank,
  findAreaThroughRank
} from '@/utils/comprehensiveStatistics.js'
export default {
  props: {
    dateType: {
      type: String,
      default: 'day'
    }
  },
  watch: {
    dateType: {
      handler(val) {
        this.getAreaAlarmRank()
        this.getAreaThroughRank()
      },
      immediate: true
    }
  },
  mounted () {
  },
  methods: {
    getAreaAlarmRank() {
      findAreaAlarmRank({ dateType: this.dateType }).then((res) => {
        this.initChart(res.data.data || [], 'areaAlarmBar')
      })
    },
    getAreaThroughRank() {
      findAreaThroughRank({ dateType: this.dateType }).then((res) => {
        this.initChart(res.data.data || [], 'areaBar')

      })
    },
    initChart(data, dom) {
      let chartDom = document.getElementById(dom)
      let myChart = echarts.init(chartDom)
      let option = {}
      if (data && data.length) {

        option = {
          yAxis: [
            {
              type: 'category',
              position: 'left', // 左侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              data: data.map((item) => item.name)
            },
            {
              type: 'category',
              position: 'right', // 右侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: true // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map((el) => el.count)
            }
          ],
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.2)'
              }
            }
          },
          grid: {
            left: '2%',
            right: '10',
            top: '13%',
            bottom: '0%',
            containLabel: true
          },
          series: [
            {
              data: data.map((el) => {
                return {
                  value: el.count
                }
              }),
              type: 'bar',
              barWidth: 10,
              barGap: '10%',
              itemStyle: {
                color: '#8BDDF5'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 0,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.setOption(option)
      // 添加柱状图鼠标点击事件
      myChart.on('click', (params) => {
        const clickedIndex = params.dataIndex
        // 修改柱状图颜色
        option.series[0].data.forEach((el) => {
          el.itemStyle = null
        })
        option.series[0].data[clickedIndex].itemStyle = {
          color: '#FFCA64' // 修改为指定颜色
        }
        this.$emit('itemClick', params.name)
        myChart.setOption(option) // 更新图表配置
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics_item {
  background: rgba(133,145,206,0.05);
  padding: 8px 8px 0;
}
.first {
  display: flex;
  .statistics_current {
    width: calc(50% - 4px);
    height: 100%;
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }

    .item_content {
      width: 100%;
      height: calc(100% - 26px);
    }
  }
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
