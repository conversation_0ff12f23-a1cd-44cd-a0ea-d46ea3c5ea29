<!--
 * @Author: hedd
 * @Date: 2023-07-04 14:35:15
 * @LastEditTime: 2025-05-01 15:09:31
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\elevatorMonitor\index.vue
 * @Description:
-->
<template>
  <div class="content">
    <div v-if="NODE_ENV_FLAG">
      <el-button class="sino-button-sure" @click="szzlyyChange('build')">SZZLYYBuild</el-button>
      <el-button class="sino-button-sure" @click="testAlarm('build')">测试打开报警详情</el-button>
      <el-button class="sino-button-sure" @click="testDeviceDetails('build')">测试打开报警详情</el-button>
    </div>
    <div class="roomInfoManagement">
      <div ref="collapseWidth" class="right-content">
        <div v-show="collapseFlag" class="bg-title">
          <!-- 表头 -->
          <div v-scrollMove class="bg-tab">
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 1 }" @click="activeTypeEvent(1)">监测总览</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 'elevatorAlarmRecord' }" @click="activeTypeEvent('elevatorAlarmRecord')">报警记录</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 3 }" @click="activeTypeEvent(3)">运行统计</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 4 }" @click="activeTypeEvent(4)">开门分析</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 5 }" @click="activeTypeEvent(5)">停靠统计</div>
          </div>
        </div>
        <div style="width: 100%; height: 100%; overflow: hidden">
          <div v-show="collapseFlag" class="bg-content room-info-box">
            <div class="sys-box">
              <MonitorOverview v-if="activeTabIndex === 1" :projectCode="projectCode" :roomData="roomData"/>
              <ElevatorRecord v-if="activeTabIndex === 'elevatorAlarmRecord'"/>
              <MonitorStatistics v-if="activeTabIndex === 3" :projectCode="projectCode"/>
              <OpenDoorAnalysis v-if="activeTabIndex === 4"  :roomData="roomData"/>
              <StopAnalysis v-if="activeTabIndex === 5"  :roomData="roomData"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog

      v-dialogDrag
      :modal="false"
      :visible="alarmDetailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="() => alarmDetailVisible = false"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="alarmDetailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
    <DeviceDetails v-if="isDeviceDetails" :dialogShow="isDeviceDetails" :roomData="roomData" :deviceId="deviceId" @deviceDetailsClose="() => isDeviceDetails = false" />
  </div>
</template>
<script>
import  EmergencyDetail  from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  name: 'elevatorMonitor',
  components: {
    MonitorOverview: () => import('../components/monitorOverview/index'),
    ElevatorRecord: () => import('../components/monitorOverview/components/elevatorRecord'),
    MonitorStatistics: () => import('../components/monitorStatistics/index'),
    OpenDoorAnalysis: () => import('../components/openDoorAnalysis/index'),
    StopAnalysis: () => import('../components/stopAnalysis/index'),
    EmergencyDetail,
    DeviceDetails: () => import('@/views/spaceManage/components/deviceDetailsNew.vue')
  },
  data() {
    const NODE_ENV_FLAG = import.meta.env.DEV
    return {
      NODE_ENV_FLAG,
      collapseFlag: true,
      activeTabIndex: 1,
      projectCode: 'DTXT',
      roomData: {},
      currentAlarmId: '',
      alarmDetailVisible: false,
      isDeviceDetails: false,
      deviceId: ''
    }
  },
  computed: {},
  created () {
    // 初始化跳转路由地址及参数 elevatorMonitor?areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}&time=2024/5/28 14:42:26
    if (this.$route.query.areaData) {
      this.roomData = {
        ...JSON.parse(this.$route.query.areaData),
        ssmCodes: this.$route.query.ssmCodes,
        projectCode: 'DTXT'
      }
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        let data = JSON.parse(event.data)
        if (data.type === 'elevatorAlarm') {
          this.currentAlarmId = data.alarmId
          this.alarmDetailVisible = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
        } else if (data.type === 'area') {
          this.roomData = {
            ...JSON.parse(data.areaData),
            ssmCodes: data.ssmCodes
          }
        } else if (data.type === 'deviceInfoNew') {
          console.log(123123123)
          this.isDeviceDetails = true
          this.deviceId = data.deviceId
        }
      })
    } catch (errpr) { }

  },
  methods: {
    testDeviceDetails () {
      this.isDeviceDetails = true
      this.deviceId = '1916338906023215106'
    },
    activeTypeEvent(val) {
      this.activeTabIndex = val
      try {
        window.chrome.webview.hostObjects.sync.bridge.ElevatorMenuName(val)
      } catch (error) {}

    },
    testAlarm () {
      this.currentAlarmId = 'BJ2024951441501725518510496'
      this.alarmDetailVisible = true
    },
    szzlyyChange (type) {
      let paramsData = {}
      if (type === 'build') {
        paramsData = {
          areaData: {
            ssmType: '3',
            parentId: '1724753193365172224',
            ssmName: '第一住院楼',
            childList: ['1724753193415503862', '1724753193415503863', '1724753193415503864', '1724753193415503865', '1724753193415503866', '1724753193415503867', '1724753193415503868', '1724753193415503869', '1724753193415503870', '1724753193415503871', '1724753193415503872', '1724753193415503873', '1724753193415503874']
          }
        }
      }
      // this.getSurveyAssetByProjectCode({ssmCodes: paramsData.ssmCodes}).then(() => {
      Object.assign(this.roomData, {
        ...paramsData
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 4px;
  box-sizing: border-box;
  .roomInfoManagement {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    .circle-btn {
      position: absolute;
      top: calc(2%);
      right: 0;
      width: 26px;
      height: 26px;
      cursor: pointer;
      margin: auto 0;
      margin-right: 10px;
      z-index: 2;
    }
    .circle-btn-top {
      background: url('~@/assets/images/center/btn-fold.png') no-repeat;
      background-size: 100% 100%;
    }
    .circle-btn-bottom {
      width: 44px;
      height: 44px;
      background: url('~@/assets/images/center/btn-unfold.png') no-repeat;
      background-size: 100% 100%;
    }
    .right-content {
      width: 24.573%;
      height: 100%;
      margin: 0 0 0 auto;
      background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 0 25px 10px 35px;
      position: relative;
      transition: width 0.3s linear;
      overflow: hidden;
      .bg-title {
        width: 100%;
        padding: 0;
        background: rgba(133, 145, 206, 0.15);
        overflow: hidden;
        color: #dceaff;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        // 不可被选中
        -webkit-user-select: none;
        .bg-tab {
          width: 100%;
          display: flex;
          overflow: hidden;
          box-sizing: border-box;
          justify-content: center;
          .tab-div {
            width: 100%;
            flex: 1;
            height: 40px;
            flex-shrink: 0;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
            color: #a4acb9;
            background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
            background-size: 100% 100%;
          }
          .tab-div:hover {
            cursor: pointer;
          }
          .is-activeTab {
            color: #b0e3fa;
            background: url('@/assets/images/qhdsys/bg-tab-xz.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        ::v-deep .el-popover {
          width: fit-content;
          min-width: 0;
          background: #374b79;
        }
        .center-empty {
          flex: 1;
          background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
          background-size: 100% 100%;
        }
        .icon-collapse {
          width: 48px;
          height: 40px;
          // flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
          &:hover {
            cursor: pointer;
          }
          img {
            margin: auto;
          }
        }
      }
      .bg-content {
        position: relative;
        box-sizing: border-box;
        //   padding: 10px 2px 2px 2px;
        //   width: 100%;
        //   height: calc(100% - 40px);
        width: calc(100% + 0px);
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
        /* scroll-view 不显示滚动条 */
      }
      .bg-content::-webkit-scrollbar {
        height: 0;
        width: 0;
      }
      .room-info-box {
        color: #fff;
        display: flex;
        flex-direction: column;
        .box-type {
          width: 100%;
          margin: auto;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          span {
            display: inline-block;
            width: fit-content;
            height: 24px;
            padding: 0 5px;
            background-color: #24396d;
            text-align: center;
            line-height: 24px;
            color: #dceaff;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            cursor: pointer;
            margin: 5px 2px 0 2px;
          }
          .type_active {
            color: #ffe3a6;
            background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .sys-box {
          width: 100%;
          // height: calc(100% - 30px);
          height: 100%;
          // flex: 1;
          .sys-box-content {
            width: 100%;
            height: 100%;
            //   padding: 10px;
            box-sizing: border-box;
          }
          // height: calc(100% - 24px);
        }
      }
    }
  }
  .sino-button-sure {
    z-index: 9999;
  }
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
