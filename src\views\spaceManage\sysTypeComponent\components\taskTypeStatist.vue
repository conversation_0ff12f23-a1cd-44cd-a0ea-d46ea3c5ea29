<template>
  <div v-loading="taskTypeStatistLoading" style="width: 100%; height: 100%">
    <div v-if="taskTypeList.length" style="width: 100%; height: 100%; overflow: hidden">
      <div id="TaskTypeStatist" ref="TaskTypeStatist"></div>
    </div>
    <div v-else class="center-center">暂无数据</div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { getPlanTypeStatistics } from '@/utils/spaceManage'
export default {
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    systemCode: {
      type: [String, Number],
      default: ''
    },
    dateType: {
      type: [String, Number],
      default: ''
    },
    currentDate: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      taskTypeStatistLoading: false,
      option: {
        legend: {},
        grid: {
          top: '0%',
          left: '5%',
          right: '5%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: [],
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return value + '个'
              }
            },
            data: []
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: [],
            barWidth: 8,
            itemStyle: {
              normal: {
                barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#0A84FFFF'
                }
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      },
      chart: null,
      taskTypeList: []
    }
  },
  watch: {
    currentDate: {
      handler: function (val) {
        if (val) {
          this.getCuringData()
        }
      },
      deep: true
    },
    // 监听模型层级数据变化
    roomData: {
      handler(val) {
        this.getCuringData()
      },
      deep: true
    }
  },
  mounted() {
    // 获取巡检任务类型数据
    this.getCuringData()
  },
  methods: {
    // 获取巡检任务类型数据
    getCuringData() {
      if (this.dateType === 'custom' && !this.currentDate.length) return
      if (!this.roomData.deviceId) {
        this.taskTypeList = []
        return
      }
      this.taskTypeStatistLoading = true
      const params = {
        dateType: this.dateType,
        startDate: this.currentDate[0] || '',
        endDate: this.currentDate[1] || '',
        systemIdentificationClassification: this.systemCode,
        systemCode: this.systemCode,
        planOrTask: 0,
        deviceId: this.roomData.deviceId
      }
      getPlanTypeStatistics(params).then((res) => {
        this.taskTypeStatistLoading = false
        const resData = res.data
        if (resData.code === '200') {
          this.taskTypeList = resData.data
          if (!this.taskTypeList.length) {
            return
          }
          const echartsData =
            resData.data.map((el) => {
              return {
                name: el.planTypeName,
                value: el.sum,
                planTypeId: el.planTypeId
              }
            })
          // 按照数值从大到小排序
          const sortedEchartsData = echartsData.sort((a, b) => a.value - b.value)
          this.option.yAxis[0].data = Array.from(sortedEchartsData, ({ name }) => name)
          this.option.series[0].data = sortedEchartsData
          this.option.yAxis[1].data = Array.from(sortedEchartsData, ({ value }) => value)
          this.$nextTick(() => {
            this.chart = echarts.init(this.$refs.TaskTypeStatist)
            this.initEcharts()
          })
        }
      })
    },
    // 初始化图表
    initEcharts() {
      this.chart.setOption(this.option)
      // 先移除点击事件 解决点击事件重复绑定
      this.chart.off('click')
      // 点击事件
      this.chart.on('click', (params) => {
        this.$emit('filterateTask', params.data.planTypeId)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
#TaskTypeStatist {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}
</style>
