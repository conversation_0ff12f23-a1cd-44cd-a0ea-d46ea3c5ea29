<template>
  <div class="statistics_item first" style="margin-bottom: 8px">
    <div class="statistics_current">
      <div class="item_title">实时监测</div>
      <div class="item_content">
        <div v-for="item in currentElevator" :key="item.key" class="content_item left">
          <div>{{item.name}}</div>
          <div class="value">{{item.value}}</div>
        </div>
      </div>
    </div>
    <div class="statistics_current">
      <div class="item_title">累计运行</div>
      <div class="item_content">
        <div class="content_item right">
          <div>累计载人数</div>
          <div class="value">{{elevatorInfo.peopleSumCount || 0}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.peopleSumRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.peopleSumRatio ? elevatorInfo.peopleSumRatio + '%' : '-'}}</span>
          </div>
        </div>
        <div class="content_item right">
          <div>运行时长</div>
          <div class="value">{{elevatorInfo.runTimeSumCount}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.runTimeSumRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.runTimeSumRatio  ? elevatorInfo.runTimeSumRatio + '%' :'-'}}</span>
          </div>
        </div>
        <div class="content_item right">
          <div>运行距离</div>
          <div class="value">{{elevatorInfo.runDistanceSumCount}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.runDistanceSumRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.runDistanceSumRatio ? elevatorInfo.runDistanceSumRatio + '%' : '-'}}</span>
          </div>
        </div>
        <div class="content_item right">
          <div>开关门次数</div>
          <div class="value">{{elevatorInfo.doorOpenedCntSumCount}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.doorOpenedCntSumRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.doorOpenedCntSumRatio ? elevatorInfo.doorOpenedCntSumRatio + '%' : ''}}</span>
          </div>
        </div>
        <div class="content_item right">
          <div>运行次数</div>
          <div class="value">{{elevatorInfo.runNumSumCount}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.runNumSumRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.runNumSumRatio ? elevatorInfo.runNumSumRatio + '%' : '-'}}</span>
          </div>
        </div>
        <div class="content_item right" @click="showDetail">
          <div>运行楼层</div>
          <div class="value">{{elevatorInfo.runFloorSumCount}}</div>
          <div class="QOQ">
            <span>环比</span>
            <img
              :src="computIcon(elevatorInfo.runFloorRatio)"
              alt=""
              width="14"
              height="14"
              style="margin: 0 4px"
            />
            <span :style="{ color: '#FF2D55' }">{{elevatorInfo.runFloorRatio ? elevatorInfo.runFloorRatio + '%' : '-'}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import shangIcon from '@/assets/images/shang.png'
import xiaIcon from '@/assets/images/xia.png'
import equalIcon from '@/assets/images/flat.png'
import { currentElevatorInfo, elevatorRunCount  } from '@/utils/comprehensiveStatistics'
export default {
  props: {
    elevatorId: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: 'day'
    }
  },
  data() {
    return {
      shangIcon,
      xiaIcon,
      equalIcon,
      elevatorInfo: {},
      currentElevator: [
        { name: '运行方向', key: 'operationStatus', value: '--' },
        { name: '当前载人数', key: 'realTimeTrafficFlow', value: '--' },
        { name: '梯门状态', key: 'doorStatus', value: '--' },
        { name: '当前楼层', key: 'floor', value: '--' },
        { name: '当前速度', key: 'speed', value: '--' },
        { name: '设备状态', key: 'onlineStatus', value: '--' }
      ]
    }
  },
  watch: {
    elevatorId (val) {
      if (val) {
        this.getelevatorRunCount()
        this.getCurrentElevatorInfo()
      }
    },
    date (val) {
      if (val) {
        this.getelevatorRunCount()
      }
    }
  },
  methods: {
    computIcon (val) {
      if (val) {
        if (val > 0) {
          return this.shangIcon
        } else if (val < 0) {
          return this.xiaIcon
        } else {
          return this.equalIcon
        }
      } else {
        return equalIcon
      }
    },
    getCurrentElevatorInfo () {
      currentElevatorInfo(this.elevatorId).then(res => {
        if (res.data.code == 200) {
          this.currentElevator.forEach(item => {
            res.data.data.forEach(el => {
              if (el.metadataTag == item.key) {
                item.value = el.valueText || '--'

              }
            })
          })
          this.currentElevator[5].value = res.data.data[0] ? res.data.data[0].onlineStatus ? '在线' : '离线' : '-'
        }
      })
    },
    getelevatorRunCount () {
      let params = {
        monitorDeviceId: this.elevatorId,
        dateType: this.date
      }
      elevatorRunCount(params).then(res => {
        if (res.data.code == 200) {
          this.elevatorInfo = res.data.result
        }
      })
    },
    showDetail () {
      let params = {
        monitorDeviceId: this.elevatorId
      }
      this.$emit('showDetail', params)
    }
  }
}
</script>
<style lang="scss" scoped>
.first {
  display: flex;
  .statistics_current {
    width: calc(50% - 4px);
    height: 100%;
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }

    .item_content {
      width: 100%;
      height: calc(100% - 26px);
      display: flex;
      flex-wrap: wrap;
      .content_item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background: rgba(133, 145, 206, 0.05);
        margin-right: 4px;
        text-align: center;
        font-size: 12px;
        color: #fff;

        .value {
          color: #ffca64;
          font-size: 16px;
          margin-top: 6px;
        }
      }
      .left {
        width: calc(50% - 4px);
        height: calc(33% - 4px);
        &:nth-child(even) {
          margin-right: 0;
        }
      }
      .right {
        width: calc(33% - 4px);
        height: calc(50% - 4px);
        &:nth-child(3n + 3) {
          margin-right: 0;
        }
        .value {
          margin-bottom: 6px;
        }
        .QOQ {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
