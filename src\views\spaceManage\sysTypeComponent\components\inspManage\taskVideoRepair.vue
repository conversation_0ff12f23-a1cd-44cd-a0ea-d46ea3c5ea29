<template>
  <div class="inspDetails">
    <div class="module-container">
      <div slot="content" class="module-content info-list" style="height: 100%">
        <div>
          巡检任务名称:<span>{{ pointInfo.taskPointName }}</span>
        </div>
        <div>
          巡检点:<span>{{ regionName }}</span>
        </div>
        <div>
          巡检时间:<span>{{ moment().format("YYYY-MM-DD HH:mm") }}</span>
        </div>
      </div>
    </div>
    <ModuleCard :showTitle="false" class="module-container-content">
      <div slot="content" class="module-content" style="height: 100%">
        <div class="taskLable">问题描述</div>
        <div class="inputTextarea">
          <el-input
            v-model="repairExplain"
            placeholder="请输入内容"
            rows="2"
            autosize
            type="textarea"
            maxlength="1000"
            class="taskInput"
          ></el-input>
        </div>
        <div>
          <div class="taskUpload">
            <div>附件图片（{{ fileList.length || "0" }} / 9）</div>
            <div @click="captureScreen"><i class="el-icon-camera" style="margin-right: 5px;"></i>截图</div>
          </div>
          <div class="taskImg">
            <el-upload
              action="image/*,video/*"
              list-type="picture-card"
              :file-list="fileList"
              :http-request="customUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :limit="9"
              :class="{ 'hide-upload': fileList.length >= 9 }"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </div>
        </div>
        <div class="repairDh">
          <div class="repairDh_box">
            <div class="repairDh_lable">联系人：</div>
            <div class="repairDh_item">
              <el-select
                v-model="person"
                placeholder="请选择"
                style="width: 100%"
                @change="handleNameChange"
              >
                <el-option
                  v-for="item in staffList"
                  :key="item.staffId"
                  :label="item.staffName"
                  :value="item.staffId"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="repairDh_box">
            <div class="repairDh_lable">联系电话：</div>
            <div class="repairDh_item">
              <el-input v-model="phoneNum" placeholder="请输入内容"></el-input>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <div class="module-footer" style="">
      <el-button @click="closeTaskResult">取消</el-button>
      <el-button @click="repairConfirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import store from '@/store'
import moment from 'moment'
import { getDutyPersonList, taskRepair, inspectionSubmit } from '@/utils/centerScreenApi'
export default {
  name: 'taskVideoResult',
  props: {
    repairData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moment,
      menuName: '',
      form: {},
      repairExplain: '',
      infoData: {},
      pointInfo: {},
      regionName: '',
      person: '', // 联系人
      phoneNum: '', // 联系方式
      teamId: '', // 报修人科室id
      staffId: '', // 报修人id
      teamName: '', // 报修人科室name
      staffList: [],
      fileList: [], // 已上传的文件列表
      attachmentUrl: [],
      dialogImageUrl: '', // 预览图片的URL
      dialogVisible: false, // 预览对话框显示状态
      inspSubImgStatus: false
    }
  },
  computed: {},
  watchch: {
    dialogVisible(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  created() {
    this.getDutyPersonList()
    let dataInfo = JSON.parse(this.repairData.pointRelease.particulars)
    this.regionName = dataInfo.regionName
    this.pointInfo = this.repairData.pointRelease
    if (this.repairData.bookArr.length > 0) {
      let arr = []
      this.repairData.bookArr.forEach((item, index) => {
        if (index == 0) {
          arr.push(index + 1 + '.' + (item.detailName || '空') + '\n')
        } else {
          arr.push('\n' + (index + 1) + '.' + (item.detailName || '空') + '\n')
        }
        if (item.maintainProjectdetailsTermReleaseList) {
          item.maintainProjectdetailsTermReleaseList.forEach(
            (item2, index2) => {
              arr.push(
                index +
                  1 +
                  '-' +
                  JSON.stringify(index2 + 1) +
                  '.巡检要点:' +
                  (item2.content || '')
              )
              if (item2.isNum == '3') {
                arr.push(',巡检内容:' + (item2.value || '空') + '\n')
              } else if (!item2.value) {
                arr.push(',巡检内容:无' + '\n')
              } else {
                arr.push(',巡检内容:' + (item2.value || '空') + '\n')
              }
            }
          )
        }
      })
      this.repairExplain = arr.join('')
    }
    // this.menuName = this.dialogData.menu == 'icis' ? '巡检' : '保养'
    // this.getTaskPointTableData();
  },
  methods: {
    // 获取值班人员
    async getDutyPersonList() {
      const dutyStartTime = moment().format('YYYY-MM-DD HH:mm:ss')
      const dutyEndTime = moment().format('YYYY-MM-DD HH:mm:ss')
      let params = {
        deviceCode: __PATH.CLIENT_CODE,
        dutyEndTime: dutyEndTime,
        dutyStartTime: dutyStartTime,
        type: '2'
      }
      getDutyPersonList(params).then((res) => {
        if (res.data.code == '200') {
          this.staffList = res.data.data.userInfoVoList
          if (this.staffList.length > 0) {
            this.person = this.staffList[0].staffName
            this.phoneNum = this.staffList[0].phone
            this.teamId = this.staffList[0].deptId
            this.staffId = this.staffList[0].staffId
            this.teamName = this.staffList[0].deptName
          }
        }
      })
    },
    // 选中联系人
    handleNameChange(val) {
      const staff = this.staffList.find((item) => item.staffName === val)
      this.phoneNum = staff ? staff.phone : ''
      this.teamId = staff ? staff.deptId : ''
      this.staffId = staff ? staff.staffId : ''
      this.teamName = staff ? staff.deptName : ''
    },
    // 移除图片
    handleRemove(file, fileList) {
      this.fileList = fileList
    },

    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    // 文件状态改变时的钩子
    async handleChange(file, fileList) {
      this.fileList = fileList
      const latestFile = fileList[fileList.length - 1]
      if (latestFile) {
        const compressedFile = await this.compressImg(
          latestFile.raw,
          0.7,
          1024
        )
        const fileUpload = {
          name: latestFile.name,
          raw: compressedFile
        }
        this.customUpload(fileUpload)
      }
    },
    // 文件超出限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning(
        `最多只能上传9张图片，您已上传${fileList.length}张`
      )
    },
    // 图片上传
    customUpload(val) {
      if (val.raw) {
        const userInfo = store.state.loginInfo
        const formData = new FormData()
        formData.append('file', val.raw)
        axios({
          method: 'post',
          url: __PATH.VUE_APP_INSP_API + '/file/upload',
          data: formData,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: userInfo.token
          }
        }).then((res) => {
          if (res.data.code == '200') {
            this.attachmentUrl.push(res.data.data.fileKey)
            this.inspSubImgStatus = true
          }
        })
      }
    },
    // 取消
    closeTaskResult() {
      this.$emit('change', 'closeTask', {})
    },
    // 屏幕截图
    async captureScreen() {
      if (this.fileList.length >= 9) {
        this.$message.warning('最多只能上传9张图片')
        return
      }
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: true
        })
        const video = document.createElement('video')
        video.srcObject = stream
        await video.play()
        const canvas = document.createElement('canvas')
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext('2d')
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        stream.getTracks().forEach((track) => track.stop())
        // base64转blob
        const dataUrl = canvas.toDataURL('image/png')
        const blob = this.dataURLtoBlob(dataUrl)
        const file = new File([blob], `screenshot_${Date.now()}.png`, {
          type: 'image/png'
        })
        // 构造 el-upload 需要的 file 对象
        const uploadFile = {
          name: file.name,
          url: dataUrl,
          raw: file
        }
        // 加入 fileList
        this.fileList.push(uploadFile)
        const fileUrl = this.fileList[this.fileList.length - 1]
        if (fileUrl) {
          this.customUpload(fileUrl)
        }
      } catch (e) {
        this.$message.error('截图失败：' + e)
      }
    },
    // base64转blob
    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    // 图片压缩
    compressImg(file, quality = 0.7, maxWH = 1024) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = (e) => {
          const img = new Image()
          img.src = e.target.result
          img.onload = () => {
            let w = img.width
            let h = img.height
            // 限制最大宽高
            if (w > maxWH || h > maxWH) {
              if (w > h) {
                h = Math.round(h * (maxWH / w))
                w = maxWH
              } else {
                w = Math.round(w * (maxWH / h))
                h = maxWH
              }
            }
            const canvas = document.createElement('canvas')
            canvas.width = w
            canvas.height = h
            const ctx = canvas.getContext('2d')
            ctx.drawImage(img, 0, 0, w, h)
            canvas.toBlob(
              (blob) => {
                // 生成压缩后的文件
                const compressedFile = new File([blob], file.name, {
                  type: file.type
                })
                resolve(compressedFile)
              },
              file.type,
              quality
            )
          }
          img.onerror = reject
        }
        reader.onerror = reject
      })
    },
    // 提交
    repairConfirm() {
      const userInfo = store.state.loginInfo
      if (this.attachmentUrl.length == 0) {
        this.$message.error('请上传附件图片', 'text')
        return false
      }
      const urls = this.attachmentUrl.length
        ? this.attachmentUrl.map((item) => {
          return item
        })
        : []
      const params = {
        unitCode: userInfo.unitCode,
        hospitalCode: userInfo.hospitalCode,
        sysForShort: 'icms',
        createByJob: '',
        jobNumber: '', // 工号
        type: '1',
        questionDescription: this.repairExplain, // 描述
        taskPointName: this.pointInfo.taskPointName, // 巡检点名称
        taskPointId: this.pointInfo.taskPointId, // 巡检点id
        callerCode: this.staffId, // 报修人id
        callerName: this.person, // 报修人name
        sourcesPhone: this.phoneNum, // 报修人电话
        workTypeCode: '10',
        attachment: JSON.stringify(urls), // 附件
        callerTape: this.recordingInfo, // 语音
        phoneNumber: this.phoneNum, // 报修人电话
        realName: this.person, // 报修人name,
        deptCode: this.teamId, // 报修人科室id
        deptName: this.teamName, // 报修人科室name
        sourcesDept: this.teamId,
        sourcesDeptName: this.teamName,
        userId: this.staffId,
        staffId: this.staffId,
        workSources: '1'
      }
      taskRepair(params).then((res) => {
        if (res.data.code == '200') {
          if (res.data.data.workNum) {
            this.submitToIcms(res.data.data.workNum)
          } else {
            this.$message.error('报修失败')
          }
        }
      })
    },
    // 反馈信息提交到icms
    submitToIcms(data) {
      const userInfo = store.state.loginInfo
      if (!this.pointInfo) return
      let param = {}
      let arr = []
      if (this.pointInfo.maintainProjectRelease.maintainProjectdetailsReleaseList.length > 0) {
        this.pointInfo.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (item) => {
            if (item.maintainProjectdetailsTermReleaseList && item.maintainProjectdetailsTermReleaseList.length > 0) {
              item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
                if (item2.isNum == '3') {
                  let obj = {
                    id: item2.id,
                    value: item2.radio
                  }
                  arr.push(obj)
                } else if (item2.isNum != '1') {
                  let obj = {
                    id: item2.id,
                    value: item2.value
                  }
                  arr.push(obj)
                }
              })
            }
          }
        )
      }
      param.unitCode = userInfo.unitCode
      param.hospitalCode = userInfo.hospitalCode
      param.answerMapList = JSON.stringify(arr)
      param.attachmentUrl = this.attachmentUrl.join(','),
      param.callerTapeUrl = '' // 语音
      param.state = '4'
      param.isBookEmpty = false
      param.guaranteeCode = data
      param.taskId = this.pointInfo.taskId
      param.taskPointReleaseId = this.pointInfo.maintainProjectRelease.taskPointReleaseId
      param.staffId = this.staffId
      param.staffName = this.staffName
      param.details = this.repairExplain
      param.spyScan = sessionStorage.getItem('whetherLocation') || 1 // 定位状态

      let taskPointTypeCode = JSON.parse(this.pointInfo.particulars).taskPointTypeCode
      let ssmId = JSON.parse(this.pointInfo.particulars).ssmId
      let zdyId = JSON.parse(this.pointInfo.particulars).id
      const innerDto = {
        unitCode: userInfo.unitCode,
        hospitalCode: userInfo.hospitalCode,
        assetsId: '',
        operationId: this.pointInfo.maintainProjectRelease.taskPointReleaseId,
        operationCode: 3, // 1:巡检 2:保养 3:报修
        operation: '报修',
        record: this.pointInfo.maintainProjectRelease.taskPointReleaseId
      }
      inspectionSubmit({ ...param, innerDto }).then(res => {
        if (res.data.code == '200') {
          this.$message.success(res.data.message)
          this.$emit('change', 'taskSuccess', {})
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/module.scss";
.inspDetails {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  .module-container {
    height: 15%;
    padding: 0px 16px;
    font-size: 16px;
    background: rgba(133, 145, 206, 0.15);
  }
  .module-container-content {
    height: 85%;
  }
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  ::v-deep .el-form-item__label {
    color: #fff;
  }
  .module-content {
    height: 100%;
  }
  .inputTextarea {
    width: 100%;
    margin-bottom: 15px;
  }
  .upload-container {
    padding: 20px;
  }

  .hide-upload ::v-deep .el-upload--picture-card {
    display: none;
  }
  .module-footer {
    text-align: center;
    padding: 10px 0px 0px 0px;
    .el-button {
      background-image: url("@/assets/images/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 35px;
      width: 30%;
    }
  }
  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  ::v-deep .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .info-list div {
    margin: 18px 0px;
  }
  .info-list span {
    margin-left: 10px;
    font-size: 14px;
  }
  .taskUpload {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 10px 10px;
    font-size: 16px;
    background: rgba(133, 145, 206, 0.15);
  }
  .taskImg {
    padding: 15px 5px 0px 15px;
  }
  .taskLable {
    padding: 10px 10px 10px 10px;
    font-size: 16px;
    background: rgba(133, 145, 206, 0.15);
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .repairDh {
    width: 100%;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 0px 16px;
    margin-top: 10px;
  }
  .repairDh_box {
    display: flex;
    width: 100%;
  }
  .repairDh_lable {
    width: 25%;
    font-size: 15px;
    margin-top: 13px;
  }
  .repairDh_item {
    width: 75%;
  }
  .taskInput {
    padding: 0px 15px;
  }
  ::v-deep .el-textarea__inner {
    border: none !important;
  }
  ::v-deep .box-card .card-body {
    overflow-y: hidden;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
}
</style>
