<template>
  <div v-if="dialogShow" class="inspManage">
    <div class="right-content">
      <div class="content">
        <div class="bg-title el-dialog__header">
          <span @click="() => (activeName = 'inspDetails')">
            <i v-if="activeName != 'inspDetails'" class="el-icon-arrow-left"></i>{{ dialogData.menu == "icis" ? "巡检" : "保养"}}{{ activeName == "inspDetails" ? "详情" : activeName == "taskVideoResult" ? "结果" : activeName == "taskVideoRepair" ? "报修" : "点详情" }}
          </span>
          <!-- <span>{{ dialogData.deviceName }}</span> -->
          <i class="el-icon-close" @click="closeDeviceDialog"></i>
        </div>
        <div class="bg-content">
          <component
            :is="activeName"
            :dialogData="newDialogData"
            :activePoint="activePoint"
            :repairData="repairData"
            :taskDetaInfo="taskDetaInfo"
            :submitTaskDeta="submitTaskDeta"
            @change="componentChange"
            @changeJumpStatus="changeJumpStatus"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'inspManage',
  components: {
    inspDetails: () => import('./components/inspManage/inspDetails.vue'),
    inspPointDetails: () =>
      import('./components/inspManage/inspPointDetails.vue'),
    inspVideoPointDetails: () =>
      import('./components/inspManage/inspVideoPointDetails.vue'),
    taskVideoResult: () =>
      import('./components/inspManage/taskVideoResult.vue'),
    taskVideoRepair: () => import('./components/inspManage/taskVideoRepair.vue')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          taskType: 'normal' // video 视频巡检 normal 常规巡检
        }
      }
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: 'inspDetails',
      activePoint: {},
      repairData: {},
      taskDetaInfo: {},
      submitTaskDeta: {},
      newDialogData: this.dialogData
    }
  },
  computed: {},
  watch: {
    dialogData: {
      handler(newVal) {
        this.activeName = 'inspDetails'
      },
      deep: false
    }
  },
  created() {},
  mounted() {},
  methods: {
    componentChange(type, data) {
      if (this.dialogData.taskType === 'video') {
        // 视频巡检
        if (type == 'videoTask') {
          this.activeName = 'inspVideoPointDetails'
          this.taskDetaInfo = data.infoData
          this.activePoint = data
        } else if (type == 'taskResult') {
          this.activeName = 'taskVideoResult'
          this.submitTaskDeta = data
        } else if (type == 'closeTask') {
          this.activeName = 'inspVideoPointDetails'
        } else if (type == 'repair') {
          this.activeName = 'taskVideoRepair'
          this.repairData = data
        } else if (type == 'taskSuccess') {
          this.activeName = 'inspDetails'
        }
      } else if (this.dialogData.taskType === 'normal') {
        // 常规巡检
        if (type === 'viewPoint') {
          this.activeName = 'inspPointDetails'
          this.activePoint = data
        }
      }
    },
    closeDeviceDialog() {
      this.$emit('configCloseDialog')
    },
    changeJumpStatus() {

      this.newDialogData.autoJump = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../style/module.scss";
.inspManage {
  position: absolute;
  top: 0;
  right: 0%;
  // width: 22%;
  width: 100%;
  height: 100%;
  // margin-top: 2%;
  height: 100%;
  .content {
    padding: 0px 25px 10px 35px;
    height: 100%;
  }
  .right-content {
    margin: 0 0 0 auto;
    width: 24.573%;
    background: url("~@/assets/images/qhdsys/bg-mask.png") no-repeat;
    height: 100%;
    background-color: transparent;
    // background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .bg-title {
      background-color: rgba(255, 224, 152, 0.12) !important;
      height: 44px;
      line-height: 44px;
      padding: 0 10px 0 1rem;
      color: #ffca64;
      font-family: TRENDS;

      width: 100%;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        width: calc(100% - 40px);
        cursor: pointer;
      }
      .el-icon-close {
        float: right;
        line-height: 44px;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 10px 2px;
      width: 100%;
      height: calc(100% - 44px);
    }
  }
}
</style>
