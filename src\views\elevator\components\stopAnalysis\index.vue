<template>
  <div class="stopAnalysis">
    <div class="search-date">
      <el-dropdown trigger="click" @command="dateTypeCommand">
        <span class="el-dropdown-link">
          {{ dateTypeList.find((v) => v.value == dateType)?.name ?? "" }}
          <i class="el-icon-caret-bottom"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in dateTypeList"
            :key="item.value"
            :command="item.value"
            :class="{ isBjxl: dateType == item.value }"
          >{{ item.name }}</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <el-date-picker
        v-model="currentDate"
        class="datePickerInput"
        popper-class="date-style"
        type="daterange"
        value-format="yyyy-MM-dd"
        :disabled="dateType != 'customize'"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="setSelectElevatorAllEvent"
        @focus="setWPFBgShow()"
        @blur="setWPFBgHide()"
      >
      </el-date-picker>
    </div>
    <div class="contnet-main">
      <ModuleCard
        title="电梯停靠统计"
        class="module-container elevatorCount"
        style="height: 35%"
      >
        <div slot="content" class="module-content" style="height: 100%">
          <div class="content-head">
            <el-select
              v-model="selectBuilding"
              collapse-tags
              placeholder="请选择楼宇"
              @change="selectBuildingChange"
            >
              <el-option
                v-for="item in buildingList"
                :key="item.id"
                :label="item.ssmName"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-select
              v-model="selectElevator"
              multiple
              collapse-tags
              placeholder="请选择电梯"
              @change="selectElevatorChange"
            >
              <el-option
                v-for="item in elevatorEquList"
                :key="item.id"
                :label="item.assetsName"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
          <div
            id="elevatorStopCountChartDom"
            style="width: 100%; height: calc(100% - 32px)"
          ></div>
        </div>
      </ModuleCard>
      <ModuleCard
        title="停靠楼层占比"
        class="module-container"
        style="height: 30%"
      >
        <div slot="content" class="module-content" style="height: 100%">
          <div
            id="stopFloorRatio"
            style="width: 100%; height: 100%; overflow: hidden"
          ></div>
        </div>
      </ModuleCard>
      <ModuleCard
        title="停靠楼层统计"
        class="module-container"
        style="height: 35%"
      >
        <div slot="title-right" class="title-right">
          <el-dropdown trigger="click" @command="sortTagChange">
            <span style="display: flex"
            ><svg-icon class="right-svg" name="right-filter"
            /></span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in sortList"
                :key="item.value"
                :command="item.value"
                :class="{ isBjxl: sortTag == item.value }"
              >{{ item.text }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div slot="content" class="module-content" style="height: 100%">
          <div id="stopFloorCount" style="width: 100%; height: 100%"></div>
        </div>
      </ModuleCard>
    </div>
    <StopRecord
      v-if="isStopRecord"
      :dialogShow="isStopRecord"
      :roomData="roomData"
      :selectData="selectData"
      @openDoorRecordClose="() => (isStopRecord = false)"
    />
  </div>
</template>

<script>
import {
  getBuildByAreaID
} from '@/utils/elevatorApi'
import { elevatorList, adsLiftFloorCountList } from '@/utils/comprehensiveStatistics'
import { elevatorStopFloorCount } from '@/utils/newIot'
import moment from 'moment'
import * as echarts from 'echarts'
moment.locale('zh-cn')
import StopRecord from './stopRecord.vue'
export default {
  name: 'stopAnalysis',
  components: {
    StopRecord
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isStopRecord: false,
      dateType: 'day', // 日期类型
      currentDate: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD')
      ], // 选中日期
      selectBuilding: '',
      selectElevator: [],
      buildingList: [],
      elevatorEquList: [],
      dateTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' },
        { value: 'costomize', name: '自定义' }
      ],
      sortTag: 1, // 排序类型
      sortList: [
        { text: '按楼层升序', value: 1 },
        { text: '按楼层降序', value: 2 },
        { text: '按从高到低', value: 3 },
        { text: '按从低到高', value: 4 }
      ],
      selectData: {},
      myChart1: null,
      myChart2: null,
      myChart3: null
    }
  },
  computed: {},
  watch: {
    roomData: {
      handler (val) {
        if (val.ssmType == 3 || val.ssmType == 4) {
          this.getBuildByAreaID()
        }
      },
      deep: true
    },
    isStopRecord(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  created() {
    if (this.roomData.ssmType == 3 || this.roomData.ssmType == 4) {
      this.getBuildByAreaID()
    }
  },
  methods: {

    // 获取楼栋
    getBuildByAreaID () {
      getBuildByAreaID({ areaId: this.roomData?.ssmCodes?.split(',')[1] }).then(
        (res) => {
          if (res.data.code == 200) {
            if (this.roomData.ssmType == 3) {
              this.buildingList = res.data.data
            } else {
              const selectId = this.roomData?.ssmCodes?.split(',').at(-1)
              this.buildingList = res.data.data.filter(
                (e) => e.id === selectId
              )
            }
            // 切换后赋空，再执行任务重新赋值，防止回显id
            this.selectBuilding = ''
            setTimeout(() => {
              this.selectBuilding = this.buildingList[0].id
              this.getElevatorList()
            }, 0)
          }
        }
      )
    },
    selectBuildingChange() {
      this.getElevatorList()
    },
    selectElevatorChange() {
      this.setSelectElevatorAllEvent()
    },
    setSelectElevatorAllEvent() {
      // 有值请求 无值赋空
      if (this.selectElevator && this.selectElevator.length) {
        this.getStopFloorGroup()
        this.getElevatorStopFloorStatistics()
      }
    },
    stopRecord(data, type) {
      let params = {
        dateType: this.dateType,
        dateRange: this.currentDate,
        buildingId: this.selectBuilding
      }
      if (type == 'stopFloorCount') {
        // 停靠楼层统计
        params = {
          ...params,
          floorName: Number(data.data.floor),
          elevatorId: this.selectElevator
        }
      } else if (type == 'stopFloorRatio') {
        params = {
          ...params,
          floorName: Number(data.data.floor),
          elevatorId: this.selectElevator
        }
      } else if (type == 'elevatorStopCount1') {
        params = {
          ...params,
          elevatorId: data.data.surveyCode.split(',')
        }
      } else if (type == 'elevatorStopCount2') {
        params = {
          ...params,
          elevatorId: this.elevatorEquList
            .find((v) => v.name == data.name)
            .value.split(',')
        }
      }
      this.selectData = params
      console.log(this.selectData)
      this.isStopRecord = true
    },

    // 电梯停靠统计
    getElevatorStopFloorStatistics () {
      let params = {
        monitorDeviceId: this.selectElevator.join(','),
        dateType: this.dateType
      }
      elevatorStopFloorCount(params).then(res => {
        if (res.data.code == 200) {
          if (res.data.result) {
            if (res.data.result.liftTotalList) {
              this.elevatorStopCountChart(
                res.data.result.liftTotalList.map((v) => {
                  return {
                    ...v,
                    value: v.floorCountSum,
                    name: v.liftName
                  }
                })
              )
            }
          }
        }
      })
    },
    // 电梯停靠统计饼图
    elevatorStopCountChart (list) {
      if (this.myChart1) {
        this.myChart1.dispose()
      }
      this.myChart1 = echarts.init(
        document.getElementById('elevatorStopCountChartDom')
      )
      let option
      if (list.length) {
        option = {
          // backgroundColor: '',
          color: ['#E88D6B', '#FFCA64', '#5E89EE', '#0A84FF', '#61E29D'],
          title: {
            text: '',
            subtext: '{a|停靠总数}',
            x: '13%',
            y: '40%',
            textStyle: {
              width: 90,
              rich: {
                a: {
                  align: 'center',
                  fontSize: 13,
                  color: '#FFFFFF'
                }
              }
            },
            subtextStyle: {
              width: 90,
              rich: {
                a: {
                  align: 'center',
                  color: 'rgba(255,255,255,0.6)',
                  fontSize: 12
                }
              }
            }
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: 'center',
            left: '50%',
            width: '60%',
            height: '90%',
            data: list.map((v) => v.name),
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 16,
            pageTextStyle: {
              color: '#fff'
            },
            textStyle: {
              //  fontSize: 18,//字体大小
              color: '#B3C2DD' //  字体颜色
            },
            formatter: function (name) {
              var oa = option.series[2].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[2].data.length; i++) {
                if (name === oa[i].name) {
                  return (
                    ' ' +
                    name +
                    '  ' +
                    oa[i].value +
                    '次  ' +
                    ((oa[i].value / num) * 100).toFixed(2) +
                    '%'
                  )
                }
              }
            }
          },
          tooltip: {
            show: false
          },
          series: [
            {
              type: 'pie',
              name: 'TypeB', // 内层细圆环2
              radius: ['56%', '57%'],
              center: ['25%', '50%'],
              hoverAnimation: false,
              clockwise: false,
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              type: 'pie',
              name: 'TypeA', // 最外层细圆环
              hoverAnimation: false,
              clockwise: false,
              radius: ['73%', '74%'],
              center: ['25%', '50%'],
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              name: 'content',
              type: 'pie',
              clockwise: false,
              radius: ['60%', '70%'],
              center: ['25%', '50%'],
              hoverAnimation: true,
              data: list,
              label: {
                show: false
              },
              itemStyle: {
                borderWidth: 2, // 间距的宽度
                borderColor: 'rgba(40,48,65,1)' // 背景色
              }
            },
            {
              // 内圆
              type: 'pie',
              radius: '52%',
              center: ['25%', '50%'],
              hoverAnimation: false,
              itemStyle: {
                color: 'rgba(133,145,206,0.15)'
              },
              label: {
                show: false
              },
              tooltip: {
                show: false
              },
              data: [100]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart1.off('mouseover')
      this.myChart1.off('mouseout')
      this.myChart1.clear()
      this.myChart1.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.myChart1.resize()
      })
      this.myChart1.on('mouseover', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + list[e.dataIndex].value + '}'
          option.title.subtext = '{a|' + list[e.dataIndex].name + '}'
          this.myChart1.setOption(option)
        }
      })
      this.myChart1.on('mouseout', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + sum + '}'
          option.title.subtext = '{a|停靠总数}'
          this.myChart1.setOption(option)
        }
      })
      this.myChart1.off('click')
      this.myChart1.on('click', (params) => {
        this.stopRecord(params, 'elevatorStopCount1')
      })
      this.myChart1.off('legendselectchanged')
      this.myChart1.on('legendselectchanged', (params) => {
        this.stopRecord(params, 'elevatorStopCount2')
      })
    },
    // 获取电梯列表
    getElevatorList() {
      let params = {
        sysOfCode: this.roomData.projectCode
      }
      elevatorList(params).then((res) => {
        if (res.data.code == 200) {
          this.elevatorEquList = res.data.data
          this.selectElevator = this.elevatorEquList.map((item) => item.id)
          this.setSelectElevatorAllEvent()
        }
      })
    },

    sortTagChange(val) {
      this.sortTag = val
      this.getStopFloorGroup()
    },
    // 楼层停靠占比
    getStopFloorGroup() {
      let params = {
        monitorDeviceId: this.selectElevator.join(','),
        dateType: this.dateType
      }
      adsLiftFloorCountList(params).then((res) => {
        if (res.data.code == 200) {
          let count = res.data.result.reduce((acc, cur) => {
            return acc + cur.floorCountSum
          }, 0)
          res.data.result.forEach((el) => {
            el.rate = ((el.floorCountSum / count) * 100).toFixed(2) + '%'
            el.name = el.floorName,
            el.value = el.floorCountSum
          })
          this.stopFloorRatioChart(res.data.result)
          this.stopFloorCountChart(res.data.result)
        }
      })
    },
    // 停靠楼岑占比treemap图
    stopFloorRatioChart (list) {
      if (this.myChart2) {
        this.myChart2.dispose()
      }
      this.myChart2 = echarts.init(document.getElementById('stopFloorRatio'))
      let option = {}
      let colors = [
        'rgba(255, 191, 41, 0.4)',
        'rgba(97, 226, 157, 0.4)',
        'rgba(88, 168, 255, 0.4)',
        'rgba(255, 128, 74, 0.4)',
        'rgba(255, 45, 85, 0.4)',
        'rgba(48, 220, 255, 0.4)',
        'rgba(164, 48, 255, 0.4)'
      ]
      if (list.length) {
        option = {
          tooltip: {
            axisPointer: {
              lineStyle: {
                color: 'rgb(30, 243, 249)'
              }
            },
            backgroundColor: '#122140',
            borderColor: '#91D2FF',
            textStyle: {
              color: '#fff'
            },
            formatter: (item) => {
              return `${item.data.floorName}层</br>停靠次数：${item.data.floorCountSum}次</br>停靠占比：${item.data.rate}`
            }
          },
          series: [
            {
              type: 'treemap',
              width: '100%',
              height: '100%',
              roam: false,
              nodeClick: false,
              levels: [
                {
                  itemStyle: {
                    gapWidth: 1
                  }
                }
              ],
              itemStyle: {
                show: true,
                textStyle: {
                  color: '#fff',
                  fontSize: 13
                },
                borderWidth: 1,
                borderColor: 'rgba(0,0,0,0)',
                emphasis: {
                  label: {
                    show: true
                  }
                }
              },
              label: {
                show: true,
                fontSize: 13,
                position: [1, 1]
              },
              breadcrumb: {
                show: false
              },
              data: list.map((v, i) => {
                return {
                  ...v,
                  name: v.floorName + '\n\n' + v.rate,
                  value: v.floorCountSum,
                  itemStyle: {
                    color: colors[i]
                  }
                }
              })
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart2.clear()
      this.myChart2.setOption(option)
      this.myChart2.off('click')
      // 点击事件
      this.myChart2.on('click', (params) => {
        this.stopRecord(params, 'stopFloorRatio')
      })
    },

    // 停靠楼岑统计柱状图
    stopFloorCountChart (list) {
      if (this.myChart3) {
        this.myChart3.dispose()
      }
      this.myChart3 = echarts.init(document.getElementById('stopFloorCount'))
      let option = {}
      if (list.length) {
        option = {
          legend: {},
          grid: {
            top: '0%',
            left: '5%',
            right: '5%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            show: false
          },
          yAxis: [
            {
              type: 'category',
              data: list.map((v) => v.name),
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
            },
            {
              type: 'category', // 坐标轴类型
              // inverse: true, // 是否是反向坐标轴
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#FFFFFFCC',
                  fontSize: '14'
                },
                formatter: (value) => {
                  return value + '次'
                }
              },
              data: list.map((v) => v.value)
            }
          ],
          series: [
            {
              type: 'bar',
              stack: 'total',
              label: {
                show: false
              },
              emphasis: {
                focus: 'series'
              },
              data: list,
              barWidth: 8,
              itemStyle: {
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#8BDDF5'
                },
                // 鼠标移入改变颜色
                emphasis: {
                  color: '#FFCA64FF'
                }
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 0,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart3.clear()
      this.myChart3.setOption(option)
      this.myChart3.off('click')
      // 点击事件
      this.myChart3.on('click', (params) => {
        this.stopRecord(params, 'stopFloorCount')
      })
    },
    // 时间类型切换
    dateTypeCommand(val) {
      const date = {
        day: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        week: [
          moment().startOf('isoWeek').format('YYYY-MM-DD'),
          moment().endOf('isoWeek').format('YYYY-MM-DD')
        ],
        month: [
          moment().startOf('month').format('YYYY-MM-DD'),
          moment().endOf('month').format('YYYY-MM-DD')
        ],
        year: [
          moment().startOf('year').format('YYYY-MM-DD'),
          moment().endOf('year').format('YYYY-MM-DD')
        ],
        customize: []
      }
      this.currentDate = date[val]
      this.sortTag = 1
      this.dateType = val
      this.setSelectElevatorAllEvent()
    },
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {}
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.stopAnalysis {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-date {
    display: flex;
    background: rgba(133, 145, 206, 0.15);
    padding: 0px 10px;

    ::v-deep .el-dropdown {
      padding: 7px 6px;

      .el-dropdown-link {
        font-size: 14px;
        font-weight: 500;
        color: #8bddf5;
        line-height: 16px;
        position: relative;
        cursor: pointer;
      }

      .el-dropdown-link::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 12px;
        background: rgba(133, 145, 206, 0.5);
        top: 50%;
        right: -6px;
        transform: translateY(-50%);
      }
    }

    ::v-deep .datePickerInput {
      flex: 1;
      padding: 8px 10px;
      height: 16px;
      box-sizing: content-box;
      background: none;
      border: none;

      .el-input__icon,
      .el-range-separator {
        line-height: 16px;
        color: #b0e3fa;
      }

      .el-range-input {
        background: none;
        color: #a4afc1;
      }
    }
  }

  .contnet-main {
    flex: 1;
    padding-top: 24px;
    // overflow: auto;
    .title-right {
      display: flex;
      align-items: center;

      .right-svg {
        color: #fff;
        font-size: 24px;
        cursor: pointer;
      }

      .el-dropdown {
        display: flex;
      }
    }

    .el-dropdown-link {
      font-size: 14px;
      font-weight: 300;
      color: #ffffff;
      line-height: 16px;

      .el-icon-arrow-down {
        font-size: 12px;
      }
    }
    .content-head {
      background: rgba(133, 145, 206, 0.15);
      display: flex;
      justify-content: space-between;
      ::v-deep(.el-select) {
        .el-input {
          .el-input__inner {
            padding: 0px 10px;
            height: 32px;
            color: #fff;
            border: none !important;
          }
          .el-input__icon {
            line-height: 20px;
          }
        }
      }
    }
    .elevatorCount {
      ::v-deep(.card-body) {
        margin: 0;
      }
    }
    .module-content {
      overflow: hidden;
    }
    #stopFloorRatio {
      position: unset !important;
    }
  }
}
:deep(.el-select__tags) {
  span {
    .el-tag {
      &:first-child {
        .el-select__tags-text {
          display: inline-block;
          width: 40px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
