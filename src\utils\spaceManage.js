import qs from 'qs'
import http from './http'

const iomsApi = __PATH.VUE_APP_IOMS_API
const idpsApi = __PATH.VUE_APP_IDPS_API
const spaceApi = __PATH.VUE_APP_SPACE_API
const warnApi = __PATH.VUE_APP_WARN_API
const iemsApi = __PATH.VUE_APP_IEMS_API
const iemcApi = __PATH.VUE_APP_IEMC_API
const imwsApi = __PATH.VUE_APP_IMWS_API
const inspApi = __PATH.VUE_APP_INSP_API
const spaceUrl = __PATH.VUE_SPACE_API
const transferApi = __PATH.VUE_TRANSFER_API
const chemicalApi = __PATH.VUE_CHEMICAL_API
const authwebApi = __PATH.VUE_APP_BASE_API
const newElevatorIot = __PATH.VUE_ELEVATOR_API
const ifemServer = __PATH.VUE_APP_IFEM_WEBSOCKET

// const adminInfo = {
//   unitCode: 'BJSYYGLJ',
//   hospitalCode: 'BJSJTY',
//   userId: 'd6aeca8128fa400c8f60f9c69402d0b3',
//   userName: '系统管理员',
//   roleCode: 'BJSJTY_systemAdminCode_IMES_CORE',
//   officeCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//   sysIdentity: 'systemAdminCode',
//   departmentAssetAdminCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//   moduleIdentity: 'IMES_CORE',
//   sysCome: 2,
//   userType: 1,
//   sysFlag: 'imes'
// }

// 获取巡检点列表(IPSM)
export function GetTaskPointReleaseList(params) {
  return http.postParamsQS(`${idpsApi}/taskPointRelease/listData`, params)
}
// 获取巡检点列表(INSP)
export function GetInspTaskPointReleaseList(params) {
  return http.postParamsQS(`${inspApi}/taskPointRelease/listData`, params)
}
// 获取保养提醒数量
export function GetInspTaskRemindNum(params) {
  return http.postParamsQS(`${inspApi}/planTaskNew/taskRemind`, params)
}
// 查看排查清单是否建立
export function CheckRiskInvestigation(params) {
  return http.postParamsQS(`${idpsApi}/riskInvestigation/checkRiskInvestigation`, params)
}
// 获取排查记录列表
export function GetRiskInvestigationRecord(params) {
  return http.postParamsQS(`${idpsApi}/riskInvestigationRecord/getRiskInvestigationRecord`, params)
}

// 获取维修统计
export function GerReckonCount(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/reckonCount`, params)
}
// 获取维修工单
export function GetWorkOrderListBySpaceRuiAn(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderListBySpaceRuiAn`, params)
}
// 获取报警列表
// export function GetAlarmHandling(params) {
//   return http.post(
//     `${warnApi}/alarmRecord/getAlarmHandling?${qs.stringify({
//       ...params
//     })}`
//   )
// }
// 获取报警列表alarm
export function GetAlarmHandling(params) {
  return http.postRequest(`${warnApi}/alarm/record/airRunPoliceDetail`, params)
}
// 获取近30天报警走势
export function GetCountAlarmTrend(params) {
  return http.post(
    `${warnApi}/alarmRecord/countAlarmTrend?${qs.stringify({
      ...params
    })}`
  )
}
// 获取报警类型分析和报警等级分析
// export function GetCountAlarm(params) {
//   return http.post(
//     `${warnApi}/alarmRecord/countAlarm?${qs.stringify({
//       ...params
//     })}`
//   )
// }
// 获取报警等级分析
export function getPolicePieByClient(params) {
  return http.postRequest(`${warnApi}/alarm/record/getPolicePieByClient`, params)
}
// 获取设备报警分析
export function GetPoliceCountByDeviceId(params) {
  return http.postRequest(`${warnApi}/alarm/record/getPoliceCountByDeviceId`, params)
}
// 获取设备基础信息
export function GetDeviceBasicInfo(params) {
  return http.postParamsQS(`${inspApi}/asset/assetDetails/getAssetDetailsByAssetIds`, params)
  // return http.postParamsQS(`${iemsApi}/assetsInfo/view`, params, {}, 'imes')
}
// 获取设备二维码
export function GetAssetQRCode(params) {
  return http.postParamsQS(`${iemsApi}/QRCode/getAssetsLabelList`, params, {}, 'imes')
}
// 获取设备二维码
export function GetDeviceLifeCycle(params) {
  return http.postParamsQS(`${iemsApi}/disposeRecord/listData`, params, {}, 'imes')
}
// 获取资产台账
export function GetAssetslistData(params) {
  return http.postParamsQS(`${iemsApi}/assetsInfo/listData`, params, {}, 'imes')
}
// 获取资产统计
export function GetAssetsStatisticsData(params) {
  return http.postParamsQS(`${iemsApi}/assetsInfo/statistics`, params, {}, 'imes')
}
// 获取工单类型统计
export function getWorkOrderTypeStatisticsBySpaceRuiAn(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderTypeStatisticsBySpaceRuiAn`, params)
}
// 获取近12月工单走势
export function getWorkOrderCountStatisticsBySpaceRuiAn(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderCountStatisticsBySpaceRuiAn`, params)
}
// 医废基本情况分析
export function getRuiAnTypeAnalysisInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getRuiAnTypeAnalysisInfo`, params)
}
// 近一周医废收集变化趋势
export function getMedicalTypeWeekTrendInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getMedicalTypeWeekTrendInfo`, params)
}
// 今日医废统计
export function getMedicalWasteCountInfoByRuiAn(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getMedicalWasteCountInfoByRuiAn`, params)
}
// 获取总面积总房间和空闲房间等
export function getRoomCountAndArea(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomCountAndArea`, params)
}
// 通过部门或者空间用途来获得房间面积的列表
export function getRoomAreaList(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomAreaList`, params)
}
// 通过部门或者空间用途来获得房间数量的列表
export function getRoomCountList(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomCountList`, params)
}
// 通过空间模型编码查看空间基础信息的详情
export function lookUpByModelCode(params) {
  return http.getQueryQS(`${spaceApi}/space/client/lookUpByModelCode`, params)
}
// 获取空间台账
export function getSpaceList(params) {
  return http.requestPost(`${spaceApi}/space/client/list`, params)
}
// 楼层code获得部门信息的列表
export function getSpaceDeptList(params) {
  return http.requestPost(`${spaceApi}/space/model/spaceDept`, params)
}
// 楼层code获得空间功能的信息列表
export function getSpaceFunctionList(params) {
  return http.requestPost(`${spaceApi}/space/model/spaceFunctionType`, params)
}
// 分页查询空间修改历史记录列表
export function getModifyHistoryList(params) {
  return http.requestPost(`${spaceApi}/space/modifyHistory/pageList`, params)
}
// 根据空间id查询管线设备分类列表
export function getCategoryList(params) {
  return http.requestPost(`${spaceApi}/pipeDevice/info/categoryList`, params)
}
// 获取运行设备台账统计及列表
export function GetDeviceAccountStatistics(params) {
  return http.requestPost(`${iemcApi}/airCondition/getSurveyCountGroup`, {
    ...params
  })
}
// 获取所有设备列表根据projectCode
export function getSurveyAssetByProjectCode(params) {
  return http.requestPost(`${iemcApi}/airCondition/getSurveyAssetByProjectCode`, {
    ...params
  })
}
// 获取所有设备列表根据projectCode
export function getTaskEquipmentStatistics(params) {
  return http.postParamsQS(`${inspApi}/planTaskNew/taskEquipmentStatistics`, {
    ...params
  })
}
// 获取设备类型列表
export function GetAssetTypeProjectCode(params) {
  return http.requestPost(`${iemcApi}/surveyAndParameter/getAssetTypeProjectCode`, {
    ...params
  })
}
// 根据设备modelCode获取对应设备id及关联实体列表
export function getSurveyInfoByModelCode(params) {
  return http.requestPost(`${iemcApi}/surveyAndParameter/getSurveyInfoByModelCode`, {
    ...params
  })
}
// 根据设备类型获取设备列表
export function GetSurveyListByAssetTypeId(params) {
  return http.requestPost(`${iemcApi}/airCondition/getSurveyGroup`, {
    ...params
  })
}
// 获取保养列表
export function GetMaintainList(params) {
  return http.postParamsQS(`${iemsApi}/imasController/getPlanTaskList`, params, {}, 'imes')
}
// 工单统计分析
export function getWorkOrderCount(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getWorkOrderCount`, params)
}
// 工单排名-类型
export function getWorkTypeCountInfo(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getWorkTypeCountInfo`, params)
}
// 工单排名-部门
export function getWorkDeptCountInfo(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getWorkDeptCountInfo`, params)
}
// 工单排名-建筑
export function getWorkOrderNumInfo(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getWorkOrderNumInfo`, params)
}
// 工单趋势
export function getWorkOrkerChange(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getWorkOrkerChange`, params)
}

// 功能用途对应的房间数./面积
export function getRoomCountAndAreaPageList(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomCountAndAreaPageList`, params)
}

// 部门所属空间统计数据
export function getRoomCountByDeptIdList(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomCountByDeptId`, params)
}

// 获取用途下面得数据
export function getDeptSpaceInfoByFunctId(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getDeptSpaceInfoByFunctId`, params)
}
// 获取建筑数据
export function getRoomCountByPidModelAndLevel(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomCountByPidModelAndLevel`, params)
}
// 获取空间弹窗列表
export function getSpaceInfoPageByModelCode(params) {
  return http.requestPost(`${spaceApi}/space/client/getSpaceInfoPageByModelCode`, params)
}
// 弹窗导出
export function exportSpaceInfoPageByModelCode(params) {
  return http.requestPostBlob(`${spaceApi}/space/client/exportSpaceInfoPageByModelCode`, params)
}
// 设备设施台账 管线
export function listBySpaceCategory(params) {
  return http.requestPost(`${spaceApi}/pipeDevice/info/listBySpaceCategory`, params)
}
// 设备设施台账 iemc设备台账
export function getDeviceCountByMenuCode(params) {
  return http.requestPost(`${iemcApi}/surveyAndParameter/getDeviceCountByMenuCode`, params)
}
// 设备设施 维修 - 后勤设备维修统计
export function getDeviceWorkOrderCount(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getDeviceWorkOrderCount`, params)
}
// 设备设施 维修 - 后勤设备工单趋势
export function getDeviceWorkOrderChange(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getDeviceWorkOrderChange`, params)
}
// 设备设施 维修 - 后勤设备维修设备列表
export function getDeviceWorkOrderList(params) {
  return http.postParamsQS(`${iomsApi}/workOrderClientController/getDeviceWorkOrderList`, params)
}
// 医用气体-监测-统计设备数量
export function getMonitorDeviceCount(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/deviceCount`, params)
}
// 医用气体-监测-根据实体类型分组查询
export function getGroupByEntityType(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/countSurveyGroupByEntityType`, params)
}
// 医用气体-监测-根据建筑分组查询
export function querySurveyBySpaceId(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/querySurveyBySpaceId`, params)
}
// 医用气体-监测-根据建筑分组查询列表
export function querySurveyListByPage(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/querySurveyListByPage`, params)
}
// 重点设备查询
export function queryAllSurveyParamList(params) {
  return http.requestPost(`${iemcApi}/electricityclient/queryAllSurveyParamList`, params)
}
// 医用气体-监测-返回实体类型分类
export function getEntityTypeByProjectCode(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryEntityTypeByProjectCode`, params)
}
// 医用气体-监测-列表
export function getGasList(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryGasList`, params)
}
// 设备设施-安全-报警统计
export function GetSecureAlarmCount(params) {
  return http.requestPost(`${warnApi}/medicalGasAlarm/alarmManagement`, params)
}
// 设备设施-安全-报警排名
export function GetSecureAlarmRank(params) {
  return http.requestPost(`${warnApi}/medicalGasAlarm/alarmRank`, params)
}
// 设备设施-安全-报警排序
export function GetSecureAlarmSort(params) {
  return http.requestPost(`${warnApi}/medicalGasAlarm/deviceAlarmSequence`, params)
}
// 获取医用气体监测，配电运行监测和智能楼宇监测数据
export function getAllSysData(params) {
  return http.requestPost(`${iemcApi}/client/getAllSysCount`, params)
}
// 获取医用气体运行监测实时数据
export function getAllRealData(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/selectRealMonitoringList`, params)
}
// 获取运行监测实时数据
export function getSecurityOtherSysList(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getSecurityOtherSysList`, params)
}
// 获取污水运行监测实时数据
export function getSewageRealList(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/sewageRealList`, params)
}
// 获取报警占比
export function getMonitoringAlarmRatio(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectDiffProjectCodeAlarm`, params)
}
// 报警类型
export function GetAlarmTypeList(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectAlarmGroupByType`, params)
}
// 获取报警记录
export function getMonitoringAlarmRecordList(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectAlarmList`, params)
}
// 获取ups详情
export function getMonitorUpsDetail(params) {
  return http.requestPost(`${iemcApi}/client/getMonitorDetail`, params)
}
// 获取楼宇/配电详情
export function getSurveyList(params) {
  return http.requestPost(`${iemcApi}/client/getSurveyList`, params)
}
// 过滤实体
export function getEntityType(params) {
  return http.requestPost(`${iemcApi}/client/getEntityType`, params)
}
// 用途所属空间统计数据
export function getRoomStatisByFunctId(params) {
  return http.getQueryQS(`${spaceApi}/space/client/getRoomStatisByFunctId`, params)
}
// 电力数据-谐波监测-查询条件
export function queryParamList(params) {
  return http.getQueryQS(`${iemcApi}/electricPower/queryParamList`, params)
}
// 电力数据-谐波监测-查询数据
export function queryHarmonicMonitorData(params) {
  return http.requestPost(`${iemcApi}/electricPower/queryHarmonicMonitorData`, params)
}
// 安防巡检-统计模板分类以及模板下计划数量
export function getPlanTypeStatistics(params) {
  return http.postParamsQS(`${inspApi}/plan/getTypeStatistics`, params)
}
// Bim-医用气体-监测-设备详情
export function queryDeviceDetail(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryDeviceDetail`, params)
}
// Bim-变配电-监测-设备详情
export function queryKeyDeviceByModelCode(params) {
  return http.requestPost(`${iemcApi}/electricityclient/queryKeyDeviceByModelCode`, params)
}
// 获取资产大类数量和占比统计
export function getEquipmentProfessionalTypeStatistics(params) {
  return http.getQueryQS(`${inspApi}/asset/assetDetails/equipmentProfessionalTypeStatistics`, params)
}
// 获取资产状态统计
export function getEquipmentStatusStatistics(params) {
  return http.postParamsQS(`${inspApi}/asset/assetDetails/equipmentStatusStatistics`, params)
}
// 获取资产列表
export function getInspAssetDetails(params) {
  return http.postParamsQS(`${inspApi}/asset/assetDetails/getAssetDetails`, params)
}
// 巡检任务右屏统计
export function getTaskListData(params) {
  return http.postParamsQS(`${inspApi}/planTaskNew/getTaskListData`, params)
}
// 巡检任务右屏统计-详情
export function getTaskDetail(params) {
  return http.postParamsQS(`${inspApi}/taskPointRelease/listData`, params)
}
// 人员工单-工单详情
export function getStaffInfoByIds(params) {
  return http.postParamsQS(`${spaceApi}/api/staffController/getStaffInfoByIds`, params)
}
// 人员工单-工单查询userId
export function getOCTeamMemberInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOCTeamMemberInfo`, params)
}
// 空间能耗列表
export function GetEnergyList(params) {
  return http.requestPost(`${iemcApi}/client/energyList`, {
    ...params
  })
}
// 空间能耗列表
export function GetEnergyAnalyze(params) {
  return http.requestPost(`${iemcApi}/client/energyAnalyze`, {
    ...params
  })
}
// 回路统计
export function getLightCountOverview(params) {
  return http.requestPost(`${iemcApi}/lightingOperationMonitoring/countOverview`, params)
}
// 回路状态统计
export function getActuatorCountOverview(params) {
  return http.requestPost(`${iemcApi}/lightingOperationMonitoring/actuatorCountOverview`, params)
}
// 回路列表
export function getGroupOperationMonitoring(params) {
  return http.requestPost(`${iemcApi}/lightingOperationMonitoring/groupOperationMonitoring`, params)
}
// 回路开关
export function getLightOpenOrClose(params) {
  return http.requestPost(`${iemcApi}/lightingOperationMonitoring/openOrClose`, params)
}
// 设备对应监测参数统计
export function getSurveyStatictiesByCode(params) {
  return http.requestPost(`${iemcApi}/electricityclient/surveyManagement`, params)
}
// 根据实体code获取对应传感器编码集合
export function getModelFormulaPointList(params) {
  return http.requestPost(`${iemcApi}/client/modelFormulaPointList`, params)
}
export function getIncidentNameGroupCountData(params) {
  return http.requestPost(`${warnApi}/alarm/record/getIncidentNameGroupCountByAlarmObjectId`, params)
}
// 施工作业-巡检列表
export function getConstructionInspectionPageList(params) {
  return http.requestPost(`${spaceUrl}/maintain/record/pageList`, params)
}
// 施工作业-作业统计
export function getAssignmentStateStatistics(params) {
  return http.requestPost(`${spaceUrl}/assignmentInfo/getAssignmentStateStatistics`, params)
}
// 施工作业-作业排序-类型
export function getAssignmentTypeStatistics(params) {
  return http.requestPost(`${spaceUrl}/assignmentInfo/getAssignmentTypeStatistics`, params)
}
// 施工作业-作业排序-科室
export function getAssignmentDeptStatistics(params) {
  return http.requestPost(`${spaceUrl}/assignmentInfo/getAssignmentDeptStatistics`, params)
}
// 施工作业-作业台账列表
export function getAssignmentInfoByPage(params) {
  return http.requestPost(`${spaceUrl}/assignmentInfo/queryAssignmentInfoByPage`, params)
}
// 施工作业-施工详情
export function getAssignmentInfo(params) {
  return http.requestPost(`${spaceUrl}/assignmentInfo/getAssignmentInfo`, params)
}
// 施工作业-获取施工类型
export function getAssignmentBusinessFormAll(params) {
  return http.requestPost(`${spaceUrl}/assignmentBusinessForm/queryAssignmentBusinessFormAll`, params)
}
// 施工作业-施工巡检统计
export function countMaintainPlanBySpaceId(params) {
  return http.requestPost(`${spaceUrl}/clientMaintainPlanController/countMaintainPlanBySpaceId`, params)
}
// 施工作业-施工巡检列表
export function constructionPatrolList(params) {
  return http.requestPost(`${spaceUrl}/clientMaintainPlanController/planList`, params)
}
// 施工作业-施工详情-巡检
export function GetDetailByLocalId(params) {
  return http.requestPost(`${spaceUrl}/clientMaintainPlanController/detailByLocalId`, params)
}
// 施工作业-施工巡检详情
export function GetInspectionDetails(params) {
  return http.requestPost(`${spaceUrl}/clientMaintainPlanController/detail`, params)
}

// 施工作业-施工巡检列表-详情
export function constructionPatrolDetail(params) {
  return http.requestPost(`${spaceUrl}/maintain/record/detail`, params)
}
// -------------------------------新监测---------------------------------
// 物流小车-获取agv网关设备id
export function GetFactoryCodeByAgv(params) {
  return http.getRequest(`${transferApi}/assetsClient/getFactoryCodeByAgv`, params)
}
// 物流小车-agv通用功能接口
export function GetGeneralFunctionByAgv(params) {
  return http.requestPost(`${transferApi}/assetsClient/device/instance/${params.deviceId}/function/${params.functionId}`, params.params)
}
// 物流小车-查询机器人列表
export function GetRobotList(params) {
  return http.requestPost(`${transferApi}/assetsClient/device/instance/list/_query`, params)
}
// 获取监测对象列表
export function GetMonitoringItemsList(params) {
  return http.requestPost(`${transferApi}/assetsClient/assets/list`, params)
}
// 获取监测对象列表
export function GetMonitoringCarryTrends(params) {
  return http.requestPost(`${transferApi}/assetsClient/carryTrends`, params)
}
// 获取监测对象统计
export function GetMonitoringItemsStatistics(params) {
  return http.postParamsQS(`${transferApi}/assetsClient/count/statistics`, params)
}
// 获取监测对象参数
export function GetMonitoringItemsParams(params) {
  return http.postParamsQS(`${transferApi}/assetsClient/assets/realTimeData`, params)
}
// 获取资产详情
export function GetMonitorAssetInfo(params) {
  return http.postParamsQS(`${transferApi}/assetsClient/assets/detailById`, params)
}
// 获取资产绑定的设备列表
export function GetAssetDeviceList (params) {
  return http.getRequest(`${transferApi}/assetsClient/query/iotAssets/${params}`, {})
}
// 获取设备下的监测参数-下拉选择
export function GetDeviceParamsList (params) {
  return http.getRequest(`${transferApi}/assetsClient/query/properties/${params.id}`, params.params)
}
// 获取监测参数历史数据
export function GetMonitoringHistoryDataOld(params) {
  return http.requestPost(`${transferApi}/assetsInfo/device/properties/history/data`, params)
}
// 获取监测参数历史数据
export function GetMonitoringHistoryDataNew(params) {
  return http.requestPost(`${transferApi}/data/center/iot/model/properties/line/statistics`, params)
}
// 获取监测参数历史数据列表
export function GetMonitoringHistoryList(params) {
  return http.requestPost(`${transferApi}/assetsClient/device/instance/${params.id}/properties/row?property=${params.property}&belongAssetsName=${params.belongAssetsName}`, params.jsonObject)
}

// 获取监测视频列表
export function GetMonitorVideoList (params) {
  return http.getRequest(`${transferApi}/assetsClient/device/relatedCamera/list`, params)
}
// 获取视频rtsp地址
export function GetVideoRTSPAddress (params) {
  return http.requestPost(`${transferApi}/assetsClient/device/previewStreaming/${params}`, {})
}
// 设备事件类型
export function GetDeviceEventsTypeList (params) {
  return http.getRequest(`${transferApi}/assetsInfo/query/events/${params}`, {})
}
// // 运行监测-事件记录
export function GetQueryInstanceEvent (params) {
  return http.requestPost(`${transferApi}/jetlinks/device-instance/${params.deviceId}/event/${params.eventId}`, params.params)
}
// 物流小车-任务列表
export function taskDeviceList (params) {
  return http.requestPost(`${transferApi}/jetlinks/device/instance/list/_query`, params)
}
export function factoryCodeByAgv (params) {
  return http.getRequest(`${transferApi}/assetsInfo/getFactoryCodeByAgv`, params)
}

export function taskList (deviceId, functionId, params) {
  return http.requestPost(`${transferApi}/jetlinks/device/instance/${deviceId}/function/${functionId}`, params)
}
// 获取所属系统/获取分类字典
export function GetClassifyDictList(params) {
  return http.getRequest(`${transferApi}/dictionary/queryCategoryByCategoryId`, params)
}
// 获取设备分类
export function GetDeviceType(params) {
  return http.getRequest(`${transferApi}/dictionary/querySubCategoryDetailsRelatedAssets`, params)
}
// 获取空间树
export function GetSpaceTree(params) {
  return http.getRequest(`${transferApi}/basedata/space/structure/spaceTree`, params)
}
// 椅位数量统计
export function GetChairStatistics(params) {
  return http.postQueryQS(`${transferApi}/chair/chairStatistics`, params)
}
// 椅位状态统计
export function GetFloorChairStatus(params) {
  return http.postQueryQS(`${transferApi}/chair/floorChairStatus`, params)
}
// 椅位科室列表
export function GetDepartmentAndChairList(params) {
  return http.requestPost(`${transferApi}/chair/departmentAndChairList`, params)
}
// 根据科室或楼层统计设备状态数量
export function GetDeviceStatusCount(params) {
  return http.requestPost(`${transferApi}/assetsClient/deviceStatusStatistics`, params)
}
// 获取设备通行记录
export function GetDeviceTravelRecord(params) {
  return http.requestPost(`${transferApi}/data/center/adsareapeopletotal/adsAreaPeopleTotal/pageList`, params)
}
// -------------------------------危化品---------------------------------
// 物资排行
export function GetMaterialRankingList(params) {
  return http.getRequest(`${chemicalApi}/warehouse/countByType`, params)
}
// 库房列表
export function GetWarehouseList(params) {
  return http.getRequest(`${chemicalApi}/warehouse/listDetail`, params)
}
// 库房详情
export function GetWarehouseDetails(params) {
  return http.getRequest(`${chemicalApi}/warehouse/detailById`, params)
}
// 库存列表
export function GetInventoryList(params) {
  return http.postFormData(`${chemicalApi}/materialinfo/listData`, params)
}
// 库房列表 - 下拉选择
export function GetInventorySelectList(params) {
  return http.postFormData(`${chemicalApi}/warehouse/listData`, params)
}
// 入库列表
export function inwarehouseDetailList(params) {
  return http.postFormData(`${chemicalApi}/inwarehouseRecord/inwarehouseDetailList`, params)
}
// 出库记录
export function outwarehouseDetailList(params) {
  return http.postFormData(`${chemicalApi}/outwarehouseRecord/outwarehouseDetailList`, params)
}
// 获取危化品类型
export function GetMaterialTypeTree(params) {
  return http.postFormData(`${chemicalApi}/materialinfo/materialTypeTree`, params)
}
// 获取危化品单位
export function GetUnitsManageLsit(params) {
  return http.postFormData(`${chemicalApi}/unitsManage/lsitData`, params)
}
// 用户信息列表
export function GetUnitManagerSelected(params) {
  return http.getRequest(`${spaceApi}/unitManager/unit-manager/getSelected`, {
    ...params
  })
}
// -------------------------------重点区域---------------------------------
// 重点区域统计
export function GetGridAreaStatistics (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/count`, params)
}
// 重点区域排行
export function GetRridAreaRank (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/rank`, params)
}
// 重点区域列表
export function GetRridAreaList (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findPage`, params)
}
// 人流监测设备列表
export function GetRridAreaDeviceList (params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findDevicePage`, params)
}
// 重点区域详情
export function GetGridAreaDetail(params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/detail`, params)
}
// 获取重点区域空间列表
export function GetKeyAreasSpaceIdList(params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findAllSpaceIdList`, params)
}
// 获取重点区域设备列表
export function GetKeyAreasDeviceIdList(params) {
  return http.postRequest(`${iemcApi}/gridArea/statistics/findAllDeviceIdList`, params)
}
// -------------------------------椅位监测---------------------------------
// 出诊记录
export function GetDoctorVisitRecord (params) {
  return http.postRequest(`${authwebApi}/visit/getDoctorVisitRecordByPage`, params)
}
// 就诊记录
export function GetPatientVisitRecord (params) {
  return http.postRequest(`${authwebApi}/visit/getPatientVisitRecordByPage`, params)
}
// 获取科室详情
export function GetDeptDetails(params) {
  return http.getRequest(`${spaceApi}/departmentManager/department-manager/selectOne`, {
    ...params
  })
}
// 获取椅位使用统计信息
export function GetChairUseStat(params) {
  return http.postRequest(`${newElevatorIot}/adszqlywsbproperties/adsZqlYwsbProperties/chairTotal`, params)
}

// 椅位列表websocket
export function GetDepartmentAndChairListWebsocket (userId) {
  return http.websocketService(`${ifemServer}/${userId}`)
}