<template>
  <div class="statistics_item first" style="margin-bottom: 8px">
    <div class="statistics_current">
      <div class="item_title">挂号渠道占比</div>
      <div id="registrationChannel" class="item_content"></div>
      <div style="" class="surplusNum">
        <span>剩余号量 </span>
        <span>{{ surplusNum }}</span>
      </div>
    </div>
    <div class="statistics_current">
      <div class="item_title">
        <span>科室挂号</span>
        <el-dropdown trigger="click" @command="typeChange">
          <span class="el-dropdown-link">
            {{ typeList.find((v) => v.value == filterType)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in typeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: filterType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div v-if="filterType == '0'" id="deptRegistration" class="item_content">

      </div>
      <div v-else id="visitBar" class="item_content">

      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { departmentCount, sourcesAtioCount  } from '@/utils/comprehensiveStatistics'
export default {
  data () {
    return {
      filterType: '0',
      typeList: [
        { value: '0', name: '挂号数' },
        { value: '1', name: '就诊数' }
      ],
      pieData: [
      ],
      surplusNum: 0,
      highlightTimer: null,
      currentIndex: -1
    }
  },
  mounted () {
    this.getSourcesAtioCount()
    this.getDepartmentCount()

  },
  methods: {
    getSourcesAtioCount() {
      sourcesAtioCount({time: dayjs().format('YYYY-MM-DD')}).then(res => {
        if (res.data.code == 200) {
          this.pieData = res.data.data.sourcesList
          this.surplusNum = res.data.data.surplusNum
          this.initPieChart(this.pieData)
        }
      })
    },
    getDepartmentCount() {
      departmentCount({time: dayjs().format('YYYY-MM-DD'), type: this.filterType}).then(res => {
        if (res.data.code == 200) {
          this.initChart(res.data.data, this.filterType == 0 ? 'deptRegistration' : 'visitBar', this.filterType)
        }
      })
    },

    typeChange (val) {
      this.filterType = val
      this.getDepartmentCount()
    },
    initPieChart (valueList) {
      const getchart = echarts.init(document.getElementById('registrationChannel'))
      getchart.resize()
      let option = {}
      if (valueList && valueList.length) {

        var xdata = valueList.map((item) => {
          return item.source || ''
        })
        var data = []
        var colorList = ['#E88D6B', '#61E29D', '#5E89EE', '#0A84FF', '#F4D982']
        for (var i = 0; i < valueList.length; i++) {
          data.push({
            value: valueList[i].sourceTotal,
            name: valueList[i].source,
            ratio: valueList[i].ratio,
            itemStyle: {
              borderWidth: 2,
              shadowBlur: 200,
              borderColor: '#0A1732',
              color: colorList[i],
              label: {
                show: true
              }
            }
          })
        }
        var seriesObj = [
          {
            name: '',
            type: 'pie',
            clockwise: false,
            center: ['20%', '50%'],
            radius: ['45%', '60%'], // 调整饼图的大小

            label: {
              show: false,
              position: 'center',
              formatter: '{c_style|{c}次}\n{b_style|{b}}',
              rich: {
                b_style: {
                  fontSize: 12,
                  fontWeight: 400,
                  color: '#fff'
                },
                c_style: {
                  padding: [0, 0, 10, 0],
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#fff'
                }
              }
            },
            emphasis: {
              scale: true,
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'normal'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          },
          {
            type: 'pie',
            radius: ['56%', '60%'],
            center: ['20%', '50%'],
            animation: false,
            emphasis: {
              scale: false
            },
            data: [
              {
                value: 100
              }
            ],
            label: {
              show: false
            },
            itemStyle: {
              color: 'rgba(133,145,206,0.15)'
            }
          },
          // 外边框
          {
            name: '外边框',
            type: 'pie',
            clockwise: false, // 顺时加载
            emphasis: {
              scale: false
            }, // 鼠标移入变大
            center: ['20%', '50%'],
            radius: ['55%', '70%'],
            label: {
              show: false
            },
            data: [
              {
                value: 0,
                name: '',
                itemStyle: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            ]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: '50%',
            animation: false,
            emphasis: {
              scale: false
            },
            center: ['20%', '50%'],
            itemStyle: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            },
            data: [
              {
                value: 100
              }
            ]
          }
        ]
        option = {
          backgroundColor: 'rgba(128, 128, 128, 0)',
          legend: {
            selectedMode: false,
            orient: 'vertical',
            type: 'scroll',
            x: 'left',
            top: '6%',
            left: '45%',
            bottom: '0%',
            data: xdata,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 13,
            pageTextStyle: {
              color: '#fff'
            },
            pageIconColor: '#fff', // 设置翻页按钮的激活颜色
            pageIconInactiveColor: '#606266', // 设置翻页按钮的非激活颜色
            textStyle: {
              fontSize: 12, // 字体大小
              color: '#B3C2DD' //  字体颜色
            },
            formatter: function (name) {
              var oa = option.series[0].data
              for (var i = 0; i < option.series[0].data.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + oa[i].name + ' ' + oa[i].value + '次' + ' ' + oa[i].ratio + '%'
                }
              }
            },
            tooltip: {
              show: false
            }
          },

          series: seriesObj
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      this.highlightTimer = null
      this.currentIndex = -1
      this.highlightTimer = setInterval(() => {
        this.startTooltipLoop(getchart, valueList.length)
      }, 1000)
      // 鼠标移入暂停
      getchart.on('mouseover', (params) => {
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentIndex
        })
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      })
      // 鼠标移出继续
      getchart.on('mouseout', (params) => {
        if (this.highlightTimer) return
        this.highlightTimer = setInterval(() => {
          this.startTooltipLoop(getchart, valueList.length)
        }, 1000)
      })
      // 先移除点击事件 解决点击事件重复绑定
      getchart.off('click')
      // 图例点击事件
      getchart.on('click', (params) => {
        const name = params.name

        getchart.setOption({
          legend: { selected: { [name]: true } } // 取消点击图例置灰
        })
        let obj = {
          data: params.data,
          type: '1'
        }
        this.$emit('itemClick', obj)
      })
    },
    startTooltipLoop(getchart, length) {
      // 取消之前高亮的图形
      getchart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      this.currentIndex = (this.currentIndex + 1) % length
      // 高亮当前图形
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      // 显示tooltip
      // getchart.dispatchAction({
      //   type: 'showTip',
      //   seriesIndex: 0,
      //   dataIndex: this.currentIndex
      // })
    },
    initChart (data, dom, type) {
      let chartDom = document.getElementById(dom)
      let myChart = echarts.init(chartDom)
      let option = null
      if (data && data.length) {
        option = {
          legend: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 12

            },
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 5

          },
          grid: {
            left: '2%',
            right: '10',
            top: '15%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: [
            {
              type: 'category',
              position: 'left', // 左侧 Y 轴
              data: data.map(item => item.departmentName),
              axisLabel: {
                color: '#fff',
                width: 80,
                overflow: 'truncate',
                formatter: function (value) {
                  if (value.length > 10) { // 根据需要调整长度
                    return value.substring(0, 10) + '...'
                  }
                  return value
                }
              }
            },
            {
              type: 'category',
              position: 'right', // 右侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: true // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map(el => {
                return `${el.value1}  ${el.value2}`
              })
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ],
          series: [
            {
              name: type == 0 ? '挂号数' : '已就诊数',
              type: 'bar',
              stack: 'total',
              label: {
                show: false
              },
              emphasis: {
                focus: 'series'
              },
              data: data.map(el => el.value1)
            },
            {
              name: type == 0 ? '剩余号' : '候诊数',
              type: 'bar',
              stack: 'total',
              label: {
                show: false
              },
              emphasis: {
                focus: 'series'
              },
              data: data.map(el => el.value2)
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.setOption(option)
      // 添加柱状图鼠标点击事件
      myChart.on('click', (params) => {
        let obj = {
          data: params.data,
          type: this.filterType
        }
        this.$emit('itemClick', obj)
      })
    }

  }
}
</script>
<style lang="scss" scoped>
#registrationChannel{
  width: 100%;
  height: calc(100% - 62px);
}
.surplusNum{
  height: 30px;
  line-height: 30px;
  width: calc(100% - 16px);
  background: rgba(133,145,206,0.05);
  color: #fff;
  text-align: center;
  font-size: 12px;
  margin:0px 8px;
  span{
    &:last-child{
      color: #FFCA64;
      font-size: 14px;
      margin-left: 10px;
    }
  }
}
.first {
  display: flex;
  .statistics_current {
    width: calc(50% - 4px);
    height: 100%;
    margin-right: 8px;
    background: rgba(133,145,206,0.05);
    padding: 8px 8px 0;
    &:last-child {
      margin-right: 0;
    }

    .item_content {
      width: 100%;
      height: calc(100% - 26px);
    }
  }
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
</style>
