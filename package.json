{"name": "iHCRS", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs", "build": "vite build", "build:szzlyy": "vite build --mode szzlyy", "build:test": "vite build --mode test", "build:outernet": "vite build --mode outernet", "build:syzxyy": "vite build --mode syzxyy", "build:ljxyy": "vite build --mode ljxyy", "build:szdeyy": "vite build --mode szdeyy", "build:bjsjtyy": "vite build --mode bjsjtyy", "build:gzzyyy": "vite build --mode gzzyyy", "build:fjslyy": "vite build --mode fjslyy", "build:sjydkqyy": "vite build --mode sjydkqyy", "build:jldyyy": "vite build --mode jldyyy", "build:216": "vite build --mode 216"}, "dependencies": {"@js-preview/docx": "^1.6.2", "@js-preview/excel": "^1.7.8", "@js-preview/pdf": "^2.0.2", "@micro-zoe/micro-app": "^1.0.0-rc.15", "@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-vue2": "^2.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/composition-api": "^1.7.2", "axios": "^0.24.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.9.4", "echarts": "^5.3.2", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "el-table-infinite-scroll": "^1.0.11", "element-ui": "^2.15.6", "enquire.js": "^2.1.6", "formidable": "^3.5.1", "indexof": "^0.0.1", "jquery": "^3.6.0", "meta2d-vue": "^1.0.32", "moment": "^2.29.3", "mutationobserver-shim": "^0.3.7", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "qs": "^6.12.1", "sass-loader": "^8.0.2", "scss": "^0.2.4", "scss-loader": "^0.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-require": "^1.1.10", "vite-plugin-svg-icons": "^2.0.1", "vue": "^2.6.11", "vue-demi": "^0.14.7", "vue-puzzle-vcode": "^1.1.9", "vue-router": "^3.2.0", "vue2-org-tree": "^1.3.6", "vuex": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-legacy": "^1.4.4", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "jquery": "^3.6.0", "less": "^3.13.1", "less-loader": "^4.1.0", "sass": "^1.28.0", "sass-loader": "^8.0.2", "vite": "4.3.9", "vite-plugin-vue2": "^2.0.3"}}