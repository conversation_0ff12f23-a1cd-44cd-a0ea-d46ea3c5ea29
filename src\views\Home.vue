<!--
 * @Author: ycw
 * @Date: 2020-05-18 11:41:56
 * @LastEditors: chenx
 * @LastEditTime: 2025-05-07 09:51:36
 * @Description: 主页，页面架构
-->

<template>
  <div class="sino-container">
    <el-container>
      <!-- <el-main class="el-main transparentBgColor"> -->
      <!-- transparentBgColor: $route.meta.bgColor === 'transparent' && !envDev, -->
      <el-main class="el-main" :class="{
        transparentBgColor: true,
        'main-padding': $route.meta.padding === 0
      }">
        <template v-if="envDev">
          <div>
            <!-- <button @click="routeChange('userManagement')">用户</button>
            <button @click="routeChange('roleManagement')">角色</button>
            <button @click="routeChange('menuManagement')">菜单</button>
            <button @click="routeChange('warnManagement')">警情</button>
            <button @click="routeChange('dictManagement')">字典</button> -->
            <button @click="routeChange('peopleWorkOrder')">人员工单</button>
            <button @click="routeChange('EmergencyTeam')">值排班信息</button>
            <button @click="routeChange('operationMonitoring')">运行监测</button>
            <button @click="routeChange('EmergencyDisposal')">应急处置</button>
            <button @click="routeChange('EmergencyDisposalOld')">应急处置old</button>
            <!-- <button @click="routeChange('warnTable')">报警弹窗</button> -->
            <!-- <button @click="routeChange('businessIOMS')">一站式</button>
            <button @click="routeChange('businessIOMSalaysis')">一站式 区域分析</button>
            <button @click="routeChange('businessIMWS')">医废</button>
            <button @click="routeChange('businessIMWSalaysis')">医废 区域分析</button>
            <button @click="routeChange('businessIPAS')">巡检</button>
            <button @click="routeChange('businessIPASalaysis')">巡检 区域分析</button> -->
            <button @click="routeChange('energyConsumption')">能耗</button>
            <!-- <button @click="routeChange('safetyOverview')">安全态势总览</button> -->
            <!-- <button @click="routeChange('intelligentOperation')">智能运行监控</button> -->
            <!-- <button @click="routeChange('overTimeScreen')">超时工单</button> -->
            <!-- <button @click="routeChange('rearAssets')">资产</button> -->
            <button @click="routeChange('elevatorMonitor')">电梯监测</button>
            <button @click="routeChange('elevatorMonitorSJY')">电梯监测四军医</button>
            <button @click="routeChange('elevatorMonitorJDY')">电梯监测吉大医</button>
            <button @click="routeChange('comprehensiveStatistics')">第四军医综合统计</button>
            <!-- <button @click="routeChange('cameraTalkBox')">对讲</button> -->
            <!-- <button @click="routeChange('elevatorEmergencyDisposal?projectCode=713e24b03094410499db0b08a2eccbcc')">事件管理</button> -->
            <!-- <button @click="routeChange('elevatorDetail?surveyEntityCode=f27e52bf7436417e84a8de41eb3269e5&projectCode=713e24b03094410499db0b08a2eccbcc')">电梯详情</button> -->

            <button
              @click="routeChange('spaceManage?modelCode=BJSJTYY01&ssmCodes=1574997196057620481,1574997196330250241&localtion=01&ssmType=3')">空间</button>
            <button @click="routeChange(url)">设备设施</button>
            <button
              @click="routeChange('spaceManage?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3')">深圳空间</button>
            <button
              @click="routeChange('airconditionSystem?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=Kongtiao&isSpace=1&menuCode=ca6bf326808146b5b37f745027f04666')">深圳设备设施</button>
            <button
              @click="routeChange('medicalManagement?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=assetsManage&isSpace=1&menuCode=ca6bf326808146b5b37f745027f04666')">医工管理</button>
            <!-- <button @click="routeChange('riskHiddenDanger')">风险隐患</button> -->
            <!-- <button @click="routeChange('equipmentAntiTheft')">设备防盗</button> -->
            <!-- <button @click="routeChange('movementTrajectory')">移动轨迹</button> -->
            <!-- <button @click="routeChange('alarmDialogIframe')">战时报警</button> -->
          </div>
          <div style="width: 100%; height: calc(100% - 23px)">
            <keep-alive :include="$store.state.keepAliveList">
              <router-view></router-view>
            </keep-alive>
          </div>
        </template>
        <template v-else>
          <keep-alive :include="$store.state.keepAliveList">
            <router-view></router-view>
          </keep-alive>
        </template>
      </el-main>
    </el-container>
  </div>
</template>

<script>

export default {
  name: 'Home',
  components: {},
  data() {
    return {
      envDev: false,
      url: 'airconditionSystem?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=space&menuCode=ca6bf326808146b5b37f745027f04666&isSpace=1&areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}',
      elevatorUrl: 'elevatorMonitorSJY?areaData={"ssmType":"3","parentId":"1724753192815718402","ssmName":"门诊楼","childList":["1724753192866050049","1732358069012168706","1732358069012168707","1732358069012168708","1732358069012168709","1732358069012168710","1732358069012168711","1732358069012168712","1732358069012168713","1732358069012168714","1732358069012168715","1732358069012168716","1732358069012168717","1732358069012168718"]}&ssmCodes=1724753192383705090,1724753192417259521,1724753192815718402'
    }
  },
  created() {
    this.envDev = process.env.NODE_ENV === 'development'
  },
  mounted() { },
  methods: {
    routeChange (path) {
      if (path == 'elevatorMonitorSJY') {
        this.$router.push({path: '/' + this.elevatorUrl})
      } else {

        this.$router.push({ path: '/' + path })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.transparentBgColor {
  background: center;
}
.main-padding {
  padding: 0;
}
</style>
