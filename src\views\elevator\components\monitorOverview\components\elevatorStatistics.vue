<template>
  <ModuleCard :title="roomData.projectCode == 'DTXT' ? '电梯统计' : '扶梯监测'" style="height: 50%">
    <div slot="content" style="height: 100%">
      <div v-if="roomData.projectCode == 'DTXT'" class="top-content">
        <div v-for="(item, index) in statisticsData" :key="index" class="statistics-item">
          <p>{{ item.name }}</p>
          <p class="green-font" :style="{'color': item.color}">{{ item.value }}</p>
        </div>
      </div>
      <div v-else class="top-content">
        <div class="vertical_count">
          <img src="@/assets/images/elevator/ic-vertical.png" alt="" />
          <div class="count_right">
            <span class="count_title">直梯数量</span>
            <span class="count_num">
              <span>{{ elevatorList.verticalElevatorOnline }}</span> / {{ elevatorList.verticalElevatorTotal }}
            </span>
          </div>
        </div>
        <div class="escalator_count">
          <img src="@/assets/images/elevator/ic-escalator.png" alt="" />
          <div class="count_right">
            <span class="count_title">扶梯数量</span>
            <span class="count_num">
              <span> {{ elevatorList.escalatorOnline }}</span> / {{ elevatorList.escalatorTotal }}
            </span>
          </div>
        </div>
      </div>
      <div class="bottom-content">
        <div class="bottom-title">
          <div>电梯视频监控</div>
          <div>
            <el-button class="elevatorClass" type="primary" @click="changePollingDialog(true)">轮巡设置</el-button>
            <el-button class="elevatorClass" type="primary" @click="changePollingTimer">{{ autoplay ? '停止' : '开始' }}</el-button>
          </div>
        </div>
        <div class="elevatorName">{{ videoName }}</div>
        <el-carousel ref="elevatorCarousel" class="elevator_carousel" :interval="oldPollingTimer * 1000" :autoplay="autoplay" arrow="always" @change="changeCarousel">
          <el-carousel-item v-for="item in elevatorOptions" :key="item.factoryCode" :name="item.factoryCode">
            <RtspCavans v-if="elevatorSelectId == item.factoryCode" ref="RtspCavans" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoUrl)" class="video_preview"></RtspCavans>
          </el-carousel-item>
        </el-carousel>
      </div>
      <el-dialog
        v-if="PollingDialogVisible"
        v-dialogDrag
        custom-class="polling-dialog"
        :modal="false"
        :close-on-click-modal="false"
        :visible.sync="PollingDialogVisible"
        :before-close="closePollingForm"
      >
        <span slot="title"> 轮巡设置 </span>
        <div class="polling-content">
          <label>轮巡时间：</label>
          <el-input v-model="newPollingTimer" oninput="value=value.replace(/^0|[^0-9]/g,'')" placeholder="请输入轮巡时间" style="width: 200px"><template slot="append">秒</template></el-input>
        </div>
        <span slot="footer">
          <el-button class="elevatorClass" type="primary" plain @click="closePollingForm">取消</el-button>
          <el-button class="elevatorClass" type="primary" @click="submitPollingForm">保存</el-button>
        </span>
      </el-dialog>
    </div>
  </ModuleCard>
</template>
<script>
import { elevatorStaticsist  } from '@/utils/newIot.js'
import { GetMonitorVideoList, GetVideoRTSPAddress, GetMonitoringItemsList } from '@/utils/spaceManage'
export default {
  components: {
    RtspCavans: () => import('./rtspCavans')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      regionCode: '',
      elevatorList: {
        verticalElevatorTotal: 0,
        verticalElevatorOnline: 0,
        escalatorTotal: 0,
        escalatorOnline: 0
      },
      // 监控列表
      elevatorOptions: [],
      elevatorSelectId: '',
      videoName: '',
      videoUrl: '', // 摄像机视频地址
      videoList: [{}],
      autoplay: true,
      pollingIndex: 0,
      PollingDialogVisible: false,
      newPollingTimer: 20, // 摄像机轮询时间'
      oldPollingTimer: 20, // 摄像机轮询默认20s
      statisticsData: [
        {
          name: '设备总数',
          value: '0',
          color: '#0A84FF'
        },
        {
          name: '正常设备',
          value: '0',
          color: '#61E29D'
        },
        {
          name: '离线设备',
          value: '0',
          color: '#FAF9FC'
        },
        {
          name: '报警设备',
          value: '0',
          color: '#FF2D55'
        }
      ]
    }
  },
  watch: {
    roomData: {
      handler: function(val) {
        const ssmCodes = val.ssmCodes?.split(',') || []
        this.regionCode = ssmCodes.at(-1)
        this.getElevatorStatistics()
        this.getCameraListByProjectCode()
      },
      deep: true
      // immediate: true
    }
  },
  mounted() {
    const ssmCodes = this.roomData.ssmCodes?.split(',') || []
    this.regionCode = ssmCodes.at(-1)
    this.getElevatorStatistics()
    this.getCameraListByProjectCode()
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'Elevator') {
          this.oldPollingTimer = data.PollingTimer
        }
      })
    } catch (error) {}
  },
  created() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetPollingTimer()
    } catch (err) {}
    this.getElevatorList()
  },
  methods: {
    // 获取电梯报警数据
    getElevatorList() {
      const params = {
        page: 1,
        pageSize: 999,
        sysOfCode: 'DTXT'
      }
      GetMonitoringItemsList(params).then((res) => {
        const { code, data } = res.data
        if (code === '200') {
          this.statisticsData[0].value = data.total || 0
          let onlineCount = 0
          let offlineCount = 0
          let alarmCount = 0
          data.records.map(i => {
            if (i.onlineStatus == 1) {
              onlineCount++
            } else {
              offlineCount++
            }
            if (i.alarmStatus == 1) {
              alarmCount++
            }
          })
          this.statisticsData[1].value = onlineCount || 0
          this.statisticsData[2].value = offlineCount || 0
          this.statisticsData[3].value = alarmCount || 0
        }
      })
    },
    // 获取电梯类型数据
    getElevatorStatistics() {
      const params = {
        projectCode: this.roomData.projectCode,
        startTime: '',
        endTime: '',
        // regionCode: this.regionCode
        regionCode: ''
      }
      elevatorStaticsist(params).then((res) => {
        if (res.data.code === '200') {
          // 直梯扶梯数量渲染
          this.elevatorList = res.data.data
        }
      })
    },

    // 摄像机轮播列表
    getCameraListByProjectCode() {
      GetMonitorVideoList({
        assetsId: '',
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1),
        sysOfCode: this.roomData.projectCode
      }).then((res) => {
        if (res.data.code == 200) {
          this.elevatorOptions = res.data.data
          if (this.elevatorOptions.length) {
            this.getVideoStreaming(this.elevatorOptions[0])
          }
        }
      })
    },
    getVideoStreaming(selectData) {
      GetVideoRTSPAddress(selectData.factoryCode).then(res => {
        this.videoUrl = res.data?.result[0]?.url || ''
      }).finally(() => {
        this.elevatorSelectId = selectData.factoryCode
        this.videoName = selectData.cameraName
      })
    },
    // 通过轮播图改变选中电梯
    changeCarousel(val) {
      const selectData = this.elevatorOptions[val]
      this.getVideoStreaming(selectData)
    },
    // 切换摄像机轮询
    changePollingTimer() {
      this.autoplay = !this.autoplay
    },
    // 打开轮训设置弹框
    changePollingDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
      this.PollingDialogVisible = true
      this.newPollingTimer = this.oldPollingTimer
    },
    // 保存轮询设置表单时长
    submitPollingForm() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.SavePollingTimer(this.newPollingTimer)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {
      }
      this.oldPollingTimer = this.newPollingTimer
      this.PollingDialogVisible = false
      if (!this.videoList.length) {
        this.autoplay = false
      } else {
        this.autoplay = true
      }
    },
    // 关闭轮询设置弹框
    closePollingForm() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.PollingDialogVisible = false
      this.newPollingTimer = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.top-content {
  display: flex;
  justify-content: space-between;
  height: 70px;
  .vertical_count,
  .escalator_count {
    width: calc(50% - 5px);
    height: 100%;
    background: #5188fc;
    display: flex;
    // flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 0 20px;
    // background: url('~@/assets/images/elevator/elevator-type-bg.png') no-repeat;
    // background-size: 100% 100%;
    background: linear-gradient(180deg, rgba(5, 22, 52, 0) 0%, rgba(5, 22, 50, 0.35) 52%, #04285c 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    border-top: 1px solid #0086fb;
    border-bottom: 1px solid #0086fb;
    box-sizing: border-box;
    .count_right {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 75%;
      padding-left: 15px;
      box-sizing: border-box;
      .count_title {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #fff;
      }
      .count_num {
        font-size: 20px;
        font-family: Arial-Bold, Arial;
        font-weight: 500;
        color: #fff;
        span {
          color: #ffd661;
        }
      }
    }
  }
  .statistics-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .green-font {
      margin-top: 10px;
      font-size: 20px;
      font-weight: bold;
    }
  }
}
.bottom-content {
  width: 100%;
  height: calc(100% - 86px);
  .bottom-title {
    width: 100%;
    height: 42px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(133, 145, 206, 0.15);
    padding: 0 8px;
    margin-top: 16px;
    .elevatorClass {
      border-radius: 0px;
      height: 28px;
    }
  }
  .elevatorName {
    width: 100%;
    height: 34px;
    line-height: 34px;
    padding-left: 16px;
    background: #030915;
  }
  .video_preview {
    width: 100%;
    height: 100%;
  }
}
::v-deep .polling-dialog {
  width: 350px;
  background: url('~@/assets/images/elevator/polling-bg.png') no-repeat;
  background-size: 100% 100%;
  .el-dialog__header {
    padding: 10px 10px 10px 26px;
    span {
      font-size: 20px;
      font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
      font-weight: 500;
      color: #dce9ff;
    }
    .el-dialog__headerbtn .el-dialog__close {
      color: #7cd0ff;
      font-size: 20px;
    }
  }
  .polling-content {
    label {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
    }
    .el-input {
      border-radius: 4px 4px 4px 4px;
      .el-input__inner {
        background: rgba(3, 23, 81, 0.5);
        border: 1px solid #193382;
        color: #fff;
      }
      .el-input-group__append,
      .el-input-group__prepend {
        padding: 0 10px;
        background: rgba(3, 23, 81, 0.5);
        border-color: #193382;
        color: #fff;
      }
    }
  }
}
.elevator_carousel {
  height: calc(100% - 94px);
  margin-bottom: 10px;
  ::v-deep .el-carousel__container {
    height: 100%;
    .el-carousel__item {
      display: flex;
      .video_preview {
        width: 100%;
        height: 100%;
        margin-bottom: 10px;
      }
    }
    .el-carousel__arrow {
      width: 32px;
      height: 32px;
      background-color: transparent;
    }
    .el-carousel__arrow:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  ::v-deep .el-carousel__indicators {
    display: none;
  }
}
</style>
