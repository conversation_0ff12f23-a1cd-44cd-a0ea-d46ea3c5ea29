<template>
  <div class="inspDetails">
    <div
      class="module-container"
      style="
        height: 30%;
        background: rgba(133, 145, 206, 0.15);
        padding: 5px 10px 0px 10px;
      "
    >
      <div slot="content" class="module-content info-list" style="height: 100%">
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item :label="menuName + '部门'">
            <el-select
              v-model="form.deptId"
              placeholder="请选择"
              @change="handledeptChange"
            >
              <el-option
                v-for="item in deptList"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="menuName + '人员'">
            <el-select
              v-model="form.staffId"
              placeholder="请选择"
              @change="handledstaffChange"
            >
              <el-option
                v-for="item in staffList"
                :key="item.staffId"
                :label="item.staffName"
                :value="item.staffId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="menuName + '时间'"
          ><span>{{
            moment().format("YYYY-MM-DD HH:mm")
          }}</span></el-form-item
          >
          <el-form-item :label="menuName + '结果'"
          ><span>{{
            submitTaskDeta.type == "2"
              ? "合格"
              : submitTaskDeta.type == "4"
                ? "异常报修"
                : "不合格"
          }}</span></el-form-item
          >
        </el-form>
      </div>
    </div>
    <ModuleCard :showTitle="false" class="module-container" style="height: 70%">
      <div slot="content" class="module-content" style="height: 100%">
        <div class="taskLable">{{ menuName }}情况说明</div>
        <div class="inputTextarea">
          <el-input
            v-model="repairExplain"
            placeholder="请输入内容"
            rows="5"
            type="textarea"
            maxlength="200"
            class="taskInput"
          ></el-input>
        </div>
        <div>
          <div class="taskUpload">
            <div>附件图片（{{ fileList.length || "0" }} / 9）</div>
            <div @click="captureScreen"><i class="el-icon-camera" style="margin-right: 5px;"></i>截图</div>
          </div>
          <div class="taskImg">
            <el-upload
              action="image/*,video/*"
              list-type="picture-card"
              :file-list="fileList"
              :http-request="customUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :on-success="handleSuccess"
              :limit="9"
              :class="{ 'hide-upload': fileList.length >= 9 }"
              @click="openFileUpload"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </div>
        </div>
      </div>
    </ModuleCard>
    <div class="module-footer" style="">
      <el-button @click="closeTaskResult">取消</el-button>
      <el-button @click="repairConfirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import store from '@/store'
import moment from 'moment'
import { inspectionSubmit, getDutyPersonList } from '@/utils/centerScreenApi'
export default {
  name: 'taskVideoResult',

  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    submitTaskDeta: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moment,
      menuName: '',
      infoData: {},
      form: {
        deptId: '',
        deptName: '',
        staffId: '',
        staffName: ''
      },
      repairExplain: '',
      particulars: '',
      fileList: [], // 已上传的文件列表
      dialogImageUrl: '', // 预览图片的URL
      dialogVisible: false, // 预览对话框显示状态
      attachmentUrl: [],
      staffList: [],
      deptList: []
    }
  },
  computed: {},
  watchch: {
    dialogVisible(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  created() {
    this.menuName = this.dialogData.menu == 'icis' ? '巡检' : '保养'
    this.getDutyPersonList()
    this.particulars = this.submitTaskDeta.particulars
    if (this.submitTaskDeta.screenshotUrl) {
      this.fileList.push(this.submitTaskDeta.screenshotUrl)
      const uploadUrl = this.fileList[this.fileList.length - 1]
      if (uploadUrl) {
        this.customUpload(uploadUrl)
      }
    }
  },
  mounted() {
    try {
      // 接收wpf传递过来的截图
      window.chrome.webview.addEventListener('message', (event) => {
        if (this.fileList.length >= 9) {
          this.$message.warning('最多只能上传9张图片')
          return
        }
        const data = JSON.parse(event.data)
        if (data.type == 'screenshot') {
          // 1. Base64解码
          const byteChars = window.atob(data.screenshotUrl)
          // 2. 创建二进制数组
          const byteArray = new Uint8Array(byteChars.length)
          for (let i = 0; i < byteChars.length; i++) {
            byteArray[i] = byteChars.charCodeAt(i)
          }
          // 3. 创建Blob并生成URL
          const blob = new Blob([byteArray], { type: 'image/jpeg' })
          const file = new File([blob], `screenshot_${Date.now()}.png`, {
            type: 'image/png'
          })
          // 构造 el-upload 需要的 file 对象
          const uploadFile = {
            name: file.name,
            url: URL.createObjectURL(blob),
            raw: file
          }
          // 加入 fileList
          this.fileList.push(uploadFile)
          const fileUrl = this.fileList[this.fileList.length - 1]
          if (fileUrl) {
            this.customUpload(fileUrl)
          }
        }
      })
    } catch (err) {
      this.$message.error('截图失败：' + err)
    }
  },
  methods: {
    // 获取值班人员
    async getDutyPersonList() {
      const dutyStartTime = moment().format('YYYY-MM-DD HH:mm:ss')
      const dutyEndTime = moment().format('YYYY-MM-DD HH:mm:ss')
      let params = {
        deviceCode: __PATH.CLIENT_CODE,
        dutyEndTime: dutyEndTime,
        dutyStartTime: dutyStartTime,
        type: '2'
      }
      getDutyPersonList(params).then((res) => {
        if (res.data.code == '200') {
          this.staffList = res.data.data.userInfoVoList
          if (this.staffList.length > 0) {
            this.form.deptId = this.staffList[0].deptId
            this.form.deptName = this.staffList[0].deptName
            this.form.staffId = this.staffList[0].staffId
            this.form.staffName = this.staffList[0].staffName
            this.deptList = []
            this.staffList.map(item => {
              if (this.deptList.map(i => i.deptId != item.deptId)) {
                this.deptList.push({
                  deptId: item.deptId,
                  deptName: item.deptName
                })
              }
            })
          }
        }
      })
    },
    handledeptChange(val) {
      const dept = this.staffList.find(item => item.deptId === val)
      this.form.deptName = dept ? dept.deptName : ''
    },
    handledstaffChange(val) {
      const staff = this.staffList.find(item => item.staffId === val)
      this.form.staffName = staff ? staff.staffName : ''
    },
    // 移除图片
    handleRemove(file, fileList) {
      this.fileList = fileList
    },

    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    // 文件状态改变时的钩子
    async handleChange(file, fileList) {
      this.fileList = fileList
      const latestFile = fileList[fileList.length - 1]
      if (latestFile) {
        const compressedFile = await this.compressImg(
          latestFile.raw,
          0.7,
          1024
        )
        const fileUpload = {
          name: latestFile.name,
          raw: compressedFile
        }
        this.customUpload(fileUpload)
      }
    },

    // 文件超出限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning(
        `最多只能上传9张图片，您已上传${fileList.length}张`
      )
    },
    // 图片上传
    customUpload(val) {
      if (val.raw) {
        const userInfo = store.state.loginInfo
        const formData = new FormData()
        formData.append('file', val.raw)
        axios({
          method: 'post',
          url: __PATH.VUE_APP_INSP_API + '/file/upload',
          data: formData,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: userInfo.token
          }
        }).then((res) => {
          if (res.data.code == '200') {
            this.attachmentUrl.push(res.data.data.fileKey)
          }
        })
      }
    },
    openFileUpload() {
      window.chrome.webview.hostObjects.sync.bridge.PatrolTrajectoryDisplayVisibility(true)
    },
    handleSuccess() {
      window.chrome.webview.hostObjects.sync.bridge.PatrolTrajectoryDisplayVisibility(false)
    },
    // 取消
    closeTaskResult() {
      this.$emit('change', 'closeTask', {})
    },
    // 屏幕截图
    async captureScreen() {
      if (this.fileList.length >= 9) {
        this.$message.warning('最多只能上传9张图片')
        return
      }
      // 通知WPF 截屏
      window.chrome.webview.hostObjects.sync.bridge.VideoScreenshot()
    },
    // base64转blob
    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    // 图片压缩
    compressImg(file, quality = 0.7, maxWH = 1024) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = (e) => {
          const img = new Image()
          img.src = e.target.result
          img.onload = () => {
            let w = img.width
            let h = img.height
            // 限制最大宽高
            if (w > maxWH || h > maxWH) {
              if (w > h) {
                h = Math.round(h * (maxWH / w))
                w = maxWH
              } else {
                w = Math.round(w * (maxWH / h))
                h = maxWH
              }
            }
            const canvas = document.createElement('canvas')
            canvas.width = w
            canvas.height = h
            const ctx = canvas.getContext('2d')
            ctx.drawImage(img, 0, 0, w, h)
            canvas.toBlob(
              (blob) => {
                // 生成压缩后的文件
                const compressedFile = new File([blob], file.name, {
                  type: file.type
                })
                resolve(compressedFile)
              },
              file.type,
              quality
            )
          }
          img.onerror = reject
        }
        reader.onerror = reject
      })
    },
    // 提交
    repairConfirm() {
      const userInfo = store.state.loginInfo
      if (this.attachmentUrl.length == 0) {
        this.$message.error('请上传附件图片', 'text')
        return false
      }
      let params = {
        unitCode: userInfo.unitCode,
        hospitalCode: userInfo.hospitalCode,
        staffId: this.form.staffId,
        staffName: this.form.staffName,
        taskPointReleaseId: this.submitTaskDeta.taskPointReleaseId,
        taskId: this.submitTaskDeta.taskId,
        spyScan: '', // 定位状态
        details: this.repairExplain,
        attachmentUrl: this.attachmentUrl.length > 0 ? this.attachmentUrl.join(',') : '',
        submitLocation: localStorage.getItem('location') || '',
        officeId: this.form.deptId,
        officeName: this.form.deptName
      }
      // 无任务书
      if (this.submitTaskDeta.answerMapList == '') {
        params.state = '2' // 合格
        params.isBookEmpty = true
        params.platformFlag = 2
        params.userName = this.loginInfo.staffName
        params.userId = this.loginInfo.staffId
      } else {
        params.state = this.submitTaskDeta.type
        params.answerMapList = this.submitTaskDeta.answerMapList
      }
      let taskPointTypeCode = JSON.parse(this.particulars).taskPointTypeCode
      let ssmId = JSON.parse(this.particulars).ssmId
      let zdyId = JSON.parse(this.particulars).id
      const innerDto = {
        unitCode: userInfo.unitCode,
        hospitalCode: userInfo.hospitalCode,
        assetsId: this.isDevice
          ? this.isDevice
          : taskPointTypeCode === 'zdy'
            ? zdyId
            : ssmId,
        operationId: this.submitTaskDeta.taskPointReleaseId,
        operationCode: '', // 1:巡检 2:保养 3:报修/巡检
        operation: '',
        record: this.$route.query.taskPointReleaseId
      }
      inspectionSubmit({ ...params, innerDto })
        .then((res) => {
          if (res.data.code == '200') {
            this.$message.success(res.data.message)
            this.$emit('change', 'taskSuccess', {})
          }
        })
        .catch((err) => {
          this.$message.error(res.data.message || '执行失败！')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/module.scss";
.inspDetails {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-list {
    position: relative;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #b0e3fa;
        line-height: 30px;
        width: 100px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 30px;
        max-width: 210px;
      }
    }
    .view-more {
      position: absolute;
      right: 10px;
      top: 8px;
      font-size: 14px;
      color: #8bddf5;
      cursor: pointer;
    }
  }
  ::v-deep .el-form-item__label {
    color: #fff;
  }
  .module-content {
    height: 100%;
  }
  .inputTextarea {
    width: 100%;
    height: 120px;
  }
  .upload-container {
    padding: 20px;
  }

  /* 隐藏上传按钮当达到最大数量时 */
  .hide-upload ::v-deep .el-upload--picture-card {
    display: none;
  }
  .module-footer {
    text-align: center;
    padding: 10px 0px 0px 0px;
    .el-button {
      background-image: url("@/assets/images/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 35px;
      width: 30%;
    }
  }
  ::v-deep .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .taskUpload {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 10px 10px;
    font-size: 16px;
    background: rgba(133, 145, 206, 0.15);
  }
  .taskImg {
    padding: 15px 5px 0px 15px;
  }
  .taskLable {
    padding: 10px 10px 10px 10px;
    font-size: 16px;
    background: rgba(133, 145, 206, 0.15);
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .taskInput {
    padding: 0px 15px;
  }
  ::v-deep .el-textarea__inner {
    border: none !important;
  }
  ::v-deep .box-card .card-body {
    overflow-y: hidden;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
}
</style>
