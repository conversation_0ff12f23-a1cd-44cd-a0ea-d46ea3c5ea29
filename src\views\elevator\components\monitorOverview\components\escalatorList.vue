<template>
  <ModuleCard
    title="电梯列表"
    class="middle-content escalatorList"
    :style="{ height: '83%' }"
  >
    <div slot="title-right" class="middle-right">
      <img
        src="@/assets/images/qhdsys/bg-gd.png"
        alt=""
        style="margin-left: 10px"
        @click="() => isDeviceList = true"
      />
    </div>
    <div slot="content" v-loading="deviceTableLoading" class="realTime-module" style="height: 100%">
      <div v-for="(item, index) in deviceList" :key="index" class="realTime-module-box">
        <div class="rtm-header-box" :style="{borderColor: item.alarmStatus == 1 ? '#FF2D55' : 'rgba(133,145,206,0.5)'}" @click="rowClick(item)">
          <div class="rtm-header-title">
            <p>
              <span>{{ item.assetsName || '-' }}</span>
              <span :style="{
                color: alarmStatusObj[item.alarmStatus]?.color,
                background: alarmStatusObj[item.alarmStatus]?.background
              }">{{ alarmStatusObj[item.alarmStatus]?.name }}</span>
            </p>
          </div>
          <div class="rtm-header-info">
            <p class="info-item">
              <span style="color: #C4F3FE;">设备类型：</span>
              <span>{{ item.deviceTypeName }}</span>
            </p>
            <p class="info-item">
              <span style="color: #C4F3FE;">在离线状态：</span>
              <span :style="{color: onlineStatusObj[item.onlineStatus]?.color}">{{ onlineStatusObj[item.onlineStatus]?.name }}</span>
            </p>
          </div>
        </div>
      </div>
      <div v-if="!deviceList.length" class="center-center">暂无数据</div>
      <MonitorDeviceListDialog
        v-if="isDeviceList"
        :isDialog="isDeviceList"
        :roomData="roomData"
        @close="() => isDeviceList = false"
      />
    </div>
  </ModuleCard>
</template>
<script>
import { GetMonitoringItemsList } from '@/utils/spaceManage'
export default {
  name: 'escalatorList',
  components: {
    MonitorDeviceListDialog: () => import('@/views/spaceManage/components/monitorDeviceListDialog.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      deviceTableLoading: false,
      deviceList: [],
      isDeviceList: false,
      onlineStatusObj: {
        1: { name: '在线', color: '#61E29D' },
        0: { name: '离线', color: '#D4DEEC' }
      },
      alarmStatusObj: {
        1: { name: '报警', color: '#FF2D55', background: 'rgba(255, 45, 85, .2)' },
        0: { name: '正常', color: '#61E29D', background: 'rgba(97, 226, 157, .2)' }
      }
    }
  },
  watch: {},
  mounted() {
    this.getElevatorList()
  },
  methods: {
    // 获取电梯报警数据
    getElevatorList() {
      const params = {
        page: 1,
        pageSize: 999,
        sysOfCode: this.roomData.projectCode
      }
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceList = res.data.data.records
        }
      })
    },
    rowClick (row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ObtainElevatorCode(row.id)
      } catch (error) {}
    }

  }
}
</script>
<style lang="scss" scoped>
.escalatorList {
  .middle-right {
    line-height: 50%;
  }
  .realTime-module {
    overflow-y: auto;
    padding: 10px 0px 0px 0px;
    .realTime-module-box {
      background: #8591ce26;
      margin-top: 10px;
      .rtm-header-box {
        padding: 16px;
        cursor: pointer;
        border: 1px solid;
        .rtm-header-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          font-size: 15px;
          span:last-child{
            margin-left: 10px;
            font-size: 14px;
            padding: 3px 8px;
            border-radius: 100px;
            line-height: 16px;
            display: inline-block;
          }
        }
        .rtm-header-info {
          font-size: 14px;
          display: flex;
          .info-item {
            width: calc(100% / 2);
            span {
              display: block;
            }
            span:last-child {
              margin-top: 8px;
              width: 100%;
              overflow:hidden;
              text-overflow:ellipsis;
              white-space:nowrap;
            }
          }
        }
      }
      .rtm-table-box {
        border: 1px solid rgba(133,145,206,0.5);
        border-top: none;
      }
    }
    .realTime-module-box:first-child {
      margin-top: 0px;
    }
  }
}
</style>
