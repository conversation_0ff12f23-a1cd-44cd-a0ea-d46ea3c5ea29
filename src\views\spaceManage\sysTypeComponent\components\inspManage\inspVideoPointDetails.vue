<template>
  <div v-loading="pageLoading" class="inspPointDetails">
    <ModuleCard :showTitle="false" class="module-container" style="height: 15%; background: rgba(133, 145, 206, 0.15)" >
      <div v-if="!isRepair" slot="content" class="module-content info-list" style="height: 100%">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p v-if="item.key == 'taskPointName'" class="item-label"> {{ menuName + item.label }}：</p>
          <p v-else class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ pointInfo[item.key] || "---" }}</p>
        </div>
      </div>
      <div v-else slot="content" class="repairTxt" style="height: 100%">
        <div class="repairTxt_lable">
          {{ menuName }}任务名称
          <span>{{ taskDetaInfo.taskName }}</span>
        </div>
        <div class="repairTxt_lable"> 周期类型：<span>{{ taskDetaInfo.cycleType == "7" ? "自定义" : taskDetaInfo.cycleType == "8" ? "单次" : taskDetaInfo.cycleType == "6" ? "每日" : taskDetaInfo.cycleType == "0" ? "每周" : taskDetaInfo.cycleType == "2" ? "每月" : taskDetaInfo.cycleType == "3" ? "季度" : "全年"}}</span></div>
        <div class="repairTxt_lable"> 责任班组<span>{{ taskDetaInfo.distributionTeamName }}</span> </div>
        <div class="repairTxt_lable">
          {{ menuName }}点：<span>{{ pointInfo.regionName }}</span>
        </div>
        <div class="repairTxt_lable">
          {{ menuName }}时间：<span>{{ taskDetaInfo.taskStartTime }}</span>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard :showTitle="false" class="module-container" style="height: 85%">
      <div slot="content" class="module-content" style="height: 100%">
        <div class="module-content-text">
          <div class="info-header">
            <p class="info-header-text">{{ menuName }}内容</p>
          </div>
          <div v-if="bookContent" class="taskBookConten">
            <!-- 日常 -->
            <el-collapse v-if="type == '0'" v-model="activeNames" class="no-arrow-collapse" >
              <el-collapse-item v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id" title="" :name="index">
                <template #title>
                  <div class="inspectionConten">
                    <span class="project"> {{ menuName }}项目 </span>
                  </div>
                  <div v-if="isRepair">
                    <el-checkbox v-model="repairOption[index]" @click.native.stop @change="radioChange(repairOption[index], index)"></el-checkbox>
                  </div>
                </template>
                <div class="daily-items">
                  {{ menuName }}内容 <div class="item-conten">{{ item.detailName }}</div>
                </div>
                <div class="daily-items">
                  标准要求
                  <div class="item-conten">
                    {{ item.maintainProjectdetails.standardRequirements }}
                  </div>
                </div>
                <div class="daily-items"> {{ menuName }}依据 <div class="item-conten">{{ item.maintainProjectdetails.inspectionBasis }}</div></div>
                <div class="step-line"></div>
              </el-collapse-item>
            </el-collapse>
            <!-- 专业 -->
            <el-collapse v-if="type == '1'" v-model="activeNames" class="no-arrow-collapse">
              <el-collapse-item v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id" title="" :name="index">
                <template #title>
                  <div class="inspectionConten">
                    <span class="project"> {{ index + 1 }}.{{ item.detailName }}</span>
                  </div>
                  <div v-if="isRepair">
                    <el-checkbox v-model="repairOption[index]" @click.native.stop @change="radioChange(repairOption[index], index)"></el-checkbox>
                  </div>
                </template>
                <div v-for="( item2, inde ) in item.maintainProjectdetailsTermReleaseList" :key="item2.id" class="projectext">
                  <div v-if="item2.isNum == '1'" class="gist type1">
                    <span>{{ menuName}}要点：{{ item2.content ? item2.content : "" }}</span>
                  </div>
                  <div v-if="item2.isNum == '2'" class="gist type2">
                    <span>{{ menuName}}要点：{{ item2.content ? item2.content : "" }}</span>
                    <el-input
                      v-model="item2.value"
                      placeholder="请输入内容"
                      maxlength="50"
                      type="textarea"
                      rows="1"
                      autosize
                      :error="item2.error"
                      @blur="checkField(item2)"
                    ></el-input>
                  </div>
                  <div v-if="item2.isNum == '0'" class="gist type3">
                    <span>{{ menuName}}要点：{{ item2.content ? item2.content + "(" + item2.rangeStart + "-" + item2.rangeEnd + (item2.einheitName ? item2.einheitName : "") + ")" : "" }}</span>
                    <el-input
                      v-model="item2.value"
                      placeholder="请输入内容"
                      maxlength="50"
                      type="number"
                      :error="item2.error"
                      @blur="checkField(item2)"
                    ></el-input>
                  </div>
                  <div v-if="item2.isNum == '3'" class="gist type4">
                    <span>{{ menuName}}要点：{{ item2.content ? item2.content : "" }}</span>
                    <el-radio-group v-model="activeChecked[index][inde]" direction="horizontal">
                      <el-radio
                        v-for="(item3, i) in item2.termJson"
                        :key="i"
                        :label="i"
                        checked-color="#ffffff"
                        icon-size="16px"
                        @click="tapRadio(item2)"
                      >{{ item3.contText }}</el-radio
                      >
                    </el-radio-group>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div v-else class="emptyBookConten">暂无{{ menuName }}内容</div>
        </div>
        <div v-if="result.state == '2'" style="background: rgba(133, 145, 206, 0.15); margin-top: 6px" >
          <div class="info-header">
            <p class="info-header-text">{{ menuName }}执行</p>
          </div>
          <div class="info-list">
            <div class="info-list-item">
              <p class="item-label">{{ menuName }}情况：</p>
              <p v-if="dialogData.menu == 'icis'" class="item-value">
                {{ excute.carryOutFlag == "0" ? "未巡检" : excute.carryOutFlag == "1" ? "已巡检" : "---" }}
              </p>
              <p v-else class="item-value">
                {{ excute.carryOutFlag == "0" ? "未保养" : excute.carryOutFlag == "1" ? "已保养" : "---" }}
              </p>
            </div>
            <div class="info-list-item">
              <p class="item-label">执行人员：</p>
              <p class="item-value">
                {{ excute.implementPersonName || "---" }}
              </p>
            </div>
            <div class="info-list-item">
              <p class="item-label">实际{{ menuName }}时间：</p>
              <p class="item-value">{{ excute.excuteTime || "---" }}</p>
            </div>
          </div>
        </div>
        <div v-if="result.state == '2'" style="background: rgba(133, 145, 206, 0.15); margin-top: 6px">
          <div class="info-header">
            <p class="info-header-text">{{ menuName }}结果</p>
          </div>
          <div class="info-list">
            <div class="info-list-item">
              <p class="item-label">结果：</p>
              <p class="item-value">
                {{ result.state == "2" ? "合格" : result.state == "3" ? "不合格" : result.state == "4" ? "异常报修" : dialogData.menu == "icis" ? "未巡检" : "未保养" }}
              </p>
            </div>
            <div class="info-list-item">
              <p class="item-label">描述：</p>
              <p class="item-value">{{ result.desc || "---" }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">图片：</p>
              <p v-if="result.attachmentUrlList && result.attachmentUrlList.length" class="item-value" >
                <span v-for="(img, index) in result.attachmentUrlList" :key="index" >
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]" >
                  </el-image>
                </span>
              </p>
              <p v-else class="item-value">暂无</p>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <div v-if="!isRepair && excute.carryOutFlag == '0'" class="module-footer">
      <el-button :disabled="isShowBtn" :class="{ grayscale: isShowBtn }" @click="confirm('2')">合格</el-button>
      <el-button :disabled="isShowBtn" :class="{ grayscale: isShowBtn }" @click="confirm('3')">不合格</el-button>
      <el-button :disabled="!bookContent" :class="{ grayscale: !bookContent }" @click="repair">报修</el-button>
    </div>
    <div v-else-if="result.state == '2'" class="module-footer" style="">
      <el-button @click="switchFun('prev')">上一个</el-button>
      <el-button @click="switchFun('next')">下一个</el-button>
    </div>
    <div v-else class="bottom-bar">
      <div class="bottom_quan">
        <div>
          <el-checkbox v-model="selectAllOption" @click.native.stop="selectAllChange(selectAllOption)"></el-checkbox>
        </div>
        <span style="margin-left: 10px">全选</span>
      </div>
      <div class="bottom_btn">
        <el-button @click="colseRepair">取消</el-button>
        <el-button @click="repairConfirm">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getInspPointDetail, getInspPointVideoDetail } from '@/utils/centerScreenApi'
export default {
  name: 'inspPointDetails',
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    activePoint: {
      type: Object,
      default: () => {}
    },
    dataInfo: {
      type: Object,
      default: () => {}
    },
    taskDetaInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: 'inspPointDetails',
      menuName: '',
      infoData: {
        workContent: [1, 2, 3, 4, 5]
      },
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: []
        }
      },
      type: '',
      isShowBtn: true,
      activeNames: [],
      activeChecked: [], // 单选默认选中
      repairOption: [], // 报修项
      isRepair: false, // 是否报修
      selectAllOption: false, // 全选
      infoList: [
        { label: '点名称', key: 'taskPointName' },
        { label: '所属区域', key: 'regionName' }
      ],
      pointInfo: {},
      tableData: [],
      taskPoint: '',
      excute: '',
      result: '',
      pageLoading: false,
      currentIndex: this.activePoint.currentIndex,
      screenshotFile: {},
      bookContent: false // 是否有模板内容
    }
  },
  computed: {},
  created() {
    this.menuName = this.dialogData.menu == 'icis' ? '巡检' : '保养'
    this.getDataDetail(
      this.activePoint.pointIds[this.activePoint.currentIndex]
    )
    this.getVideoDataDetail()
  },
  mounted() {
    setInterval(() => {
      this.isShowBtn = false
    }, 5000)
  },
  methods: {
    switchFun(type) {
      if (type === 'next') {
        this.currentIndex =
          this.currentIndex + 1 < this.activePoint.pointIds.length
            ? this.currentIndex + 1
            : 0
      } else if (type === 'prev') {
        this.currentIndex =
          this.currentIndex - 1 >= 0
            ? this.currentIndex - 1
            : this.activePoint.pointIds.length - 1
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.PatrolRealView(
          JSON.stringify({
            type: 'point',
            id: this.activePoint.pointIds[this.currentIndex]
          })
        )
      } catch (error) {}
      this.getDataDetail(this.activePoint.pointIds[this.currentIndex])
    },
    getVideoDataDetail() {
      this.pageLoading = true
      getInspPointVideoDetail({ id: this.activePoint.id }).then((res) => {
        this.pageLoading = false
        const { pointRelease } = res.data.data
        this.pointRelease = pointRelease
        if (pointRelease.maintainProjectRelease) {
          this.bookContent = true
          this.type = pointRelease.maintainProjectRelease.equipmentTypeId
          pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
            (i, index) => {
              this.activeNames.push(index)
              this.repairOption.push(false)
              if (i.maintainProjectdetailsTermReleaseList) {
                const iii = []
                i.maintainProjectdetailsTermReleaseList.forEach((j) => {
                  if (j.isNum == '3') {
                    j.termJson.forEach((k, ind) => {
                      if (k.isDefault == '0') {
                        iii.push(ind)
                      }
                    })
                  } else {
                    iii.push('')
                  }
                })
                this.activeChecked.push(iii)
              }
            }
          )
        }
        setTimeout(() => {
          this.$emit('changeJumpStatus')
        }, 500)
      })
    },
    getDataDetail(id) {
      this.pageLoading = true
      getInspPointDetail({ id }).then((res) => {
        this.pageLoading = false
        const data = res.data
        if (data.code === '200') {
          Object.assign(this, {
            taskPoint: data.data.taskPoint,
            tableData: data.data.project.projectdetailsReleaseList,
            excute: data.data.excute,
            result: data.data.result,
            pointInfo: {
              regionName: data.data.taskPoint.particulars
                ? JSON.parse(data.data.taskPoint.particulars).regionName
                : '',
              taskPointName: data.data.taskPoint.taskPointName || ''
            }
          })
          if (this.tableData && this.tableData.length) {
            this.tableData.forEach((val, index) => {
              if (val.isNum === '3') {
                val.radioTextArr = JSON.parse(val.termJson)
              }
            })
          }
        }
      })
    },
    // 输入校验
    checkField(item2) {
      if (!item2.value || item2.value.length == 0) {
        item2.error = '内容不能为空'
      } else {
        item2.error = ''
      }
      if (
        item2.value &&
        (item2.value < parseFloat(item2.rangeStart) ||
          item2.value > parseFloat(item2.rangeEnd))
      ) {
        item2.outRange = true
      } else {
        item2.outRange = false
      }
      this.$forceUpdate()
    },
    tapRadio(item) {
      this.$forceUpdate()
    },
    // 单选
    radioChange(e, index) {
      const hasEmptyString = this.repairOption.every((i) => i === true)
      if (!hasEmptyString) {
        this.selectAllOption = false
      } else {
        this.selectAllOption = true
      }
    },
    // 全选
    selectAllChange(e) {
      this.repairOption = []
      if (!e) {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (i, index) => {
            this.repairOption.push(true)
          }
        )
      } else {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (i, index) => {
            this.repairOption.push(false)
          }
        )
      }
    },
    // 合格/不合格
    confirm(type) {
      const arr = []
      let outRange = false
      let isFile = false
      if (this.bookContent) {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (i, ind) => {
            if (this.type == 0) {
              // 日常
              const item = {}
              item.id = i.id
              item.normal = type
              arr.push(item)
            } else {
              i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
                if (item2.isNum == '3') {
                  let obj = {
                    id: item2.id,
                    value:
                      item2.termJson[this.activeChecked[ind][index]].contText,
                    normal: ''
                  }
                  arr.push(obj)
                } else if (item2.isNum != '1') {
                  if (!item2.value || item2.value.length == 0) {
                    item2.error = '内容不能为空'
                  } else {
                    item2.error = ''
                  }
                  this.$forceUpdate()
                  if (item2.outRange) {
                    outRange = true
                  }
                  let obj = {
                    id: item2.id,
                    value: item2.value,
                    normal: ''
                  }
                  arr.push(obj)
                }
              })
            }
          }
        )
        const isDevice = JSON.parse(this.pointRelease.particulars).assetsId
          ? JSON.parse(this.pointRelease.particulars).assetsId
          : false
        if (this.type == '0') {
          // 日常
          this.captureScreen(type, arr, isDevice, isFile)
        } else {
          // 专业
          if (type == '3') {
            // 不合格
            this.captureScreen(type, arr, isDevice, isFile)
          } else {
            // 校验所有填写项是否填写
            if (arr.some((i) => !i.value)) {
              this.$message.error('请先完成任务书！')
            } else {
              if (outRange) {
                this.$message.error('输入范围异常！')
              } else {
                this.captureScreen(type, arr, isDevice, isFile)
              }
            }
          }
        }
      } else {
        this.captureScreen(type, arr, false, isFile)
      }
    },
    toSubmisPage(type, arr, isDevice, isFile, screenUrl) {
      this.$emit('change', 'taskResult', {
        taskId: this.pointRelease.taskId,
        // taskPointReleaseId:
        //   this.pointRelease.maintainProjectRelease.taskPointReleaseId,
        taskPointReleaseId: this.activePoint.id,
        particulars: this.pointRelease.particulars,
        type,
        isDevice,
        answerMapList: JSON.stringify(arr),
        taskPointName: this.pointRelease.taskPointName,
        screenshotUrl: screenUrl
      })
    },
    // 报修
    repair() {
      this.isRepair = true
    },
    colseRepair() {
      this.isRepair = false
    },
    // 报修提交
    repairConfirm() {
      if (this.repairOption.every((i) => !i)) {
        this.$message.error('未选择报修项')
        this.isRepair = false
      } else {
        let bookArr = []
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (item, ind) => {
            if (this.repairOption[ind]) {
              bookArr.push(item)
            }
            if (this.type == 1) {
              item.maintainProjectdetailsTermReleaseList.forEach(
                (item2, index) => {
                  if (item2.isNum == '3') {
                    item2.value =
                      item2.termJson[this.activeChecked[ind][index]].contText
                  }
                }
              )
            }
          }
        )
        this.$emit('change', 'repair', {
          bookArr,
          pointRelease: this.pointRelease,
          isDevice: JSON.parse(this.pointRelease.particulars).assetsId
            ? JSON.parse(this.pointRelease.particulars).assetsId
            : false
        })
      }
    },
    // 点击合格/不合格截取当前屏幕
    async captureScreen(type, arr, isDevice, isFile) {
      // 通知WPF 截屏
      window.chrome.webview.hostObjects.sync.bridge.VideoScreenshot()
      // 接收wpf传递过来的截图
      try {
        window.chrome.webview.addEventListener('message', (event) => {
          const data = JSON.parse(event.data)
          if (data.type == 'screenshot') {
            // 1. Base64解码
            const byteChars = window.atob(data.screenshotUrl)
            // 2. 创建二进制数组
            const byteArray = new Uint8Array(byteChars.length)
            for (let i = 0; i < byteChars.length; i++) {
              byteArray[i] = byteChars.charCodeAt(i)
            }
            // 3. 创建Blob并生成URL
            const blob = new Blob([byteArray], { type: 'image/jpeg' })
            const file = new File([blob], `screenshot_${Date.now()}.png`, {
              type: 'image/png'
            })
            console.log('file', file)
            // 构造 el-upload 需要的 file 对象
            const screenUrl = {
              name: file.name,
              url: URL.createObjectURL(blob),
              raw: file
            }
            this.toSubmisPage(type, arr, isDevice, isFile, screenUrl)
          }
        })
      } catch (errpr) {
        this.$message.error('截图失败：' + errpr)
      }
    },
    dataURLtoFile(dataurl, filename) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], filename, { type: mime })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/module.scss";
.inspPointDetails {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133, 145, 206, 0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #ffffff;
    }
    .info-header-status {
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      padding: 3px 8px;
      border-radius: 100px;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #b0e3fa;
        line-height: 30px;
        width: 100px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 30px;
        flex: 1;
        img {
          margin-top: 10px;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
  .taskBookConten {
    height: 100%;
    width: 100%;
    padding: 0px 16px;
  }
  .emptyBookConten {
    padding: 20px 0;
    font-size: 14px;
    text-align: center;
    background-color: transparent;
  }
  .no-arrow-collapse .el-collapse-item__arrow {
    display: none !important;
  }
  ::v-deep .el-collapse,
  ::v-deep .el-collapse-item__wrap,
  ::v-deep .el-collapse-item__header {
    background: rgba(133, 145, 206, 0.04) !important;
    border: none;
  }
  .grayscale {
    filter: grayscale(100%);
  }
  ::v-deep .el-textarea__inner {
    border: none !important;
  }
  ::v-deep .el-input__inner {
    border: none !important;
  }
  ::v-deep .el-radio__label {
    color: #ffffff;
  }
  ::v-deep .el-checkbox__inner {
    border-radius: 50%;
  }
  .project {
    color: #ffffff;
    font-size: 14px;
  }
  .daily-items {
    color: #ffffff;
  }
  .projectext {
    color: #ffffff;
  }
  .module-content-text {
    background: rgba(133, 145, 206, 0.08) !important;
  }
  .repairTxt span {
    font-size: 14px;
  }
  .bottom-bar {
    display: flex;
    width: 100%;
    justify-content: space-between;
    padding: 0px 15px;
  }
  .bottom_quan {
    display: flex;
    font-size: 14px;
    margin: 14px 20px 0px -20px;
  }
  .bottom_btn .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .inspectionConten {
    margin-right: 30px;
  }
  .repairTxt {
    font-size: 16px;
    padding: 0px 16px;
  }
  .repairTxt div {
    margin: 8px 0px;
  }
  ::v-deep .box-card .card-body {
    overflow-y: hidden;
  }
  .module-footer {
    text-align: center;
    padding: 10px 0px 0px 0px;
    .el-button {
      background-image: url("@/assets/images/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 35px;
      width: 30%;
      transition: filter 0.5s ease;
    }
  }
}
</style>
<style lang="scss">
</style>
