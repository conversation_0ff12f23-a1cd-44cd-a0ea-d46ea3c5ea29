<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="deviceDetailsDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">设备详情</span>
      </template>
      <div class="dialog-content">
        <div v-scrollMove class="bg-tab">
          <div v-for="(item, index) in tabList" :id="item.value" :key="index" class="tab-div" :class="{ 'is-activeTab': activeTab === item.value }" @click="activeTabEvent(item.value, index)">
            {{ item.name }}
          </div>
        </div>
        <div class="main-content">
          <component :is="currentComponent" :ref="currentComponent" :roomData="roomData" :deviceId="deviceId"></component>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'deviceDetailsNew',
  components: {
    deviceBaseInfo: () => import('./components/deviceBaseInfoNew.vue'),
    deviceAlarmHistory: () => import('./components/deviceAlarmHistoryNew.vue'),
    deviceParamHistory: () => import('./components/deviceParamHistoryNew.vue'),
    deviceEventRecord: () => import('./components/deviceEventRecord.vue'),
    deviceTravelRecord: () => import('./components/deviceTravelRecord.vue'),
    deviceUsageRecord: () => import('./components/deviceUsageRecord.vue')
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    deviceId: {
      type: String,
      default: ''
    },
    roomData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      activeTab: 1,
      tabList: [
        {
          value: 1,
          name: '基础信息',
          component: 'deviceBaseInfo'
        },
        {
          value: 2,
          name: '报警历史',
          component: 'deviceAlarmHistory'
        },
        {
          value: 3,
          name: '参数历史',
          component: 'deviceParamHistory'
        },
        {
          value: 4,
          name: '事件记录',
          component: 'deviceEventRecord'
        }
      ]
    }
  },
  computed: {
    currentComponent() {
      return this.tabList.find((e) => e.value === this.activeTab)?.component
    }
  },
  created() {
    this.initModule()
  },
  destroyed() {
    this.$tools.showDialog()
    // try {
    //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    // } catch (error) {}
  },
  methods: {
    initModule() {
      this.$tools.showDialog()
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      // } catch (error) {}
      if (this.roomData.tabName == 'KeyAreas') {
        this.tabList.push({
          value: 5,
          name: '通行记录',
          component: 'deviceTravelRecord'
        })
      } else if (this.roomData.tabName == 'SpaceChair') {
        this.tabList.push({
          value: 6,
          name: '使用记录',
          component: 'deviceUsageRecord'
        })
      }
    },
    // tab切换
    activeTabEvent(val, index) {
      this.activeTab = val
    },
    // 取消按钮
    closeDialog() {
      this.$emit('deviceDetailsClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .deviceDetailsDialog {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
  }
  .dialog-content {
    width: 100%;
    height: 96%;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    .bg-tab {
      display: flex;
      overflow: hidden;
      box-sizing: border-box;
      .tab-div {
        width: 88px;
        height: 40px;
        flex-shrink: 0;
        line-height: 40px;
        text-align: center;
        font-size: 16px;
        color: #a4acb9;
        background: url('@/assets/images/bg-tab.png') no-repeat;
        cursor: pointer;
      }
      .is-activeTab {
        color: #b0e3fa;
        background: url('@/assets/images/bg-tab-xz.png') no-repeat;
      }
    }
    .main-content {
      flex: 1;
      overflow: hidden;
      margin-top: 16px;
    }
  }
}
</style>
