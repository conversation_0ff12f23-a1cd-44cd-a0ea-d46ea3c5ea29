<template>
  <ModuleCard
    title="电梯列表"
    class="middle-content"
    :style="{ height: '50%' }"
  >
    <div slot="title-right" class="middle-right">
      <img
        src="@/assets/images/qhdsys/bg-gd.png"
        alt=""
        style="margin-left: 10px"
        @click="showDeviceList"
      />
    </div>
    <div slot="content" style="height: 98%">
      <el-table
        :data="list"
        class="table-center-transfer"
        height="100%"
        :cell-style="$tools.setCell(4)"
        :header-cell-style="$tools.setHeaderCell(4)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @row-click="rowClick"
      >
        <el-table-column
          prop="assetsName"
          label="电梯名称"
          width="105"
          show-overflow-tooltip=""
        >
        </el-table-column>
        <el-table-column label="当前层" show-overflow-tooltip  width="75">
          <template slot-scope="scope">
            {{ setLabel("floor", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="载人数" show-overflow-tooltip  width="75">
          <template slot-scope="scope">
            {{ setLabel("realTimeTrafficFlow", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="上下行" show-overflow-tooltip width="75">
          <template slot-scope="scope">
            {{ setLabel("operationStatus", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="开门次数" show-overflow-tooltip width="80">
          <template slot-scope="scope">
            {{ setLabel("doorOpenedCnt", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
      </el-table>
      <MonitorDeviceListDialog
        v-if="isDeviceList"
        :isDialog="isDeviceList"
        :roomData="roomData"
        @close="closeDialog"
      />
    </div>
  </ModuleCard>
</template>
<script>
import { GetMonitoringItemsList } from '@/utils/spaceManage'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')
export default {
  components: {
    MonitorDeviceListDialog: () =>
      import('@/views/spaceManage/components/monitorDeviceListDialog.vue')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      list: [],
      allAlarmVisible: false,
      paramsData: {},
      isDeviceList: false
    }
  },
  watch: {},
  mounted() {
    this.getElevatorList()
  },
  methods: {
    setLabel(key, arr) {
      let obj = arr.find((el) => el.metadataTag === key)
      if (key === 'operationStatus') {
        return obj ? (obj.value === '1' ? '上行' : '下行') : '-'
      }
      return obj ? obj.value : '-'
    },
    // 获取电梯报警数据
    getElevatorList() {
      const params = {
        page: 1,
        pageSize: 999,
        sysOfCode: this.roomData.projectCode
      }
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code === '200') {
          this.list = res.data.data.records
        }
      })
    },
    showDeviceList () {
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(true)
      } catch (error) {}
      this.isDeviceList = true
    },
    closeDialog() {
      this.isDeviceList = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(false)
      } catch (error) {}
    },
    rowClick (row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ObtainElevatorCode(row.id)
      } catch (error) {}
    }

  }
}
</script>
<style lang="scss" scoped>
.middle-right {
  line-height: 50%;
}
:deep(.el-table__body-wrapper) {
  height: calc(100% - 48px);
}
:deep(.el-table--striped) .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important;
  /* def2ff f2faff */
}
</style>
