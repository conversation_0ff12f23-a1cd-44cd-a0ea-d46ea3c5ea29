<template>
  <div class="component PanelCameraSelect">
    <el-button v-if="hasSource" class="new-edition PanelCameraSelect__btn" @click="showPickerPanel = true">
      <img src="@/assets/images/common/icon-camera.png" style="vertical-align: bottom;">
      {{ title }}({{ source.length }})</el-button>

    <div v-show="showPickerPanel" class="PanelCameraSelect__wrapper">
      <div class="PanelCameraSelect__mask" @click="showPickerPanel = false">
      </div>
      <div class="PanelCameraSelect__panel">
        <div class="PanelCameraSelect__head">
          <div class="PanelCameraSelect__title">{{ title }}({{ cameraList.length }})</div>
          <div class="PanelCameraSelect__close" @click="showPickerPanel = false"></div>
        </div>
        <div class="PanelCameraSelect__list">
          <el-checkbox-group v-model="selectIds" :max="single ? 2 : 4" :min="1">
            <el-checkbox v-for="camera in cameraList" :key="camera.id" :label="camera.id" :title="camera.name">
              <img src="@/assets/images/common/icon-camera.png">
              {{ camera.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'PanelCameraSelect',
  components: {
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    source: Array,
    value: Array,
    single: {
      type: Boolean,
      default: false
    },
    title: String
  },
  data() {
    return {
      showPickerPanel: false
    }
  },
  emits: ['change', 'update:visible'],
  computed: {
    cameraList: function () {
      const list = this.source || []
      return list.map((item, index) => {
        return {
          id: item.id,
          name: item.name
        }
      })
    },
    selectIds: {
      get: function () {
        return this.value.map(x => x.id)
      },
      set: function (value) {
        const tmpValue = this.single ? value.splice(-1) : value
        const items = this.source.filter(x => tmpValue.includes(x.id))
        this.$emit('change', items)
      }
    },
    // 是否有可播放的资源
    hasSource: function () {
      return this.source && this.source.length > 0
    }
  },
  methods: {
    // 关闭弹窗
    hidePanel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.component.PanelCameraSelect {

  .PanelCameraSelect {
    &__btn {
      height: 28px;
    }

    &__head {
      padding: 16px;
      display: flex;
      justify-content: space-between;
    }

    &__title {
      font-size: 15px;
    }

    &__close {
      height: 13px;
      width: 13px;
      background: url('~@/assets/images/common/icon-close.png');
      cursor: pointer;
    }

    &__list {
      padding: 0 16px 16px;

      .el-checkbox::v-deep {
        display: flex;
        flex-flow: row-reverse nowrap;
        justify-content: space-between;
        align-items: center;
        margin: 8px 0 20px;
        overflow: hidden;

        .el-checkbox__label {
          color: #fff;
          padding-left: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          >img {
            margin-right: 4px;
          }
        }

        .el-checkbox__input.is-checked+.el-checkbox__label {
          color: #FFCA64;
        }

        .el-checkbox__inner {
          border-color: #A4AFC1;
          border-radius: 0;
        }
      }
    }

    &__mask {
      position: fixed;
      z-index: 2000;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    &__panel {
      z-index: 2001;
      background: rgba(6, 16, 44, 0.9);
      position: absolute;
      top: 42px;
      right: 0;
      height: 500px;
      width: 200px;
      overflow: auto;
      border: 1px solid rgba(133, 145, 206, 0.5);
    }
  }
}
</style>
