/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:39:39
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2023-10-31 15:47:34
 */
import http from './http'
/**
 *  @parms resquest 请求地址 例如：http://************:8088/request/...
 *  @param '/testIp'代表vue-cil中config，index.js中配置的代理
 */
// const baseApi = __PATH.VUE_APP_BASE_API
// const planApi = __PATH.VUE_APP_RESERVE_PLAN_API
const iomsApi = __PATH.VUE_APP_IOMS_API
const imwsApi = __PATH.VUE_APP_IMWS_API
const idpsApi = __PATH.VUE_APP_IDPS_API
const iemcApi = __PATH.VUE_APP_IEMC_API
const iemcWsApi = __PATH.VUE_APP_IEMC_WS_API
const inspApi = __PATH.VUE_APP_INSP_API
const warnApi = __PATH.VUE_APP_WARN_API
const ICIS_API = __PATH.VUE_SPACE_API

// 服务事项top5
export function getMaintenanceMattersTop5(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getMaintenanceMattersTop5`, params)
}
// 科室报修量
export function getDepartmentRepairInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getDepartmentRepairInfo`, params)
}
// 服务工单趋势分析
export function getWorkOrderTrendInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderTrendInfo`, params)
}
// 区域综合维修量分析
export function getAreaMaintenanceInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getAreaMaintenanceInfo`, params)
}
// 区域综合维修事项分析
export function getAreaMaintenanceMatterInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getAreaMaintenanceMatterInfo`, params)
}
// 医废处置动态
export function getMedicalWasteDisposalInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getMedicalWasteDisposalInfo`, params)
}
// 医废产生趋势图
export function getMedicalWasteTrendInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getMedicalWasteTrendInfo`, params)
}
// 医废类型分析
export function getTypeAnalysisInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getTypeAnalysisInfo`, params)
}
// 科室医废类型分析
export function getDepartmentMedicalWasteInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getAreaDepartmentMedicalWasteInfo`, params)
}
// 科室医废重量分析
export function getDepartWeightMedicalWasteInfo(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getDepartWeightMedicalWasteInfo`, params)
}
// 科室收集列表
export function getDepartMedicalWasteList(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getDepartMedicalWasteList`, params)
}
// 科室收集列表
export function getMedicalWasteDataPageList(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/getMedicalWasteDataPageList`, params)
}
// 科室收集列表追溯
export function gatherRetrospect(params) {
  return http.postParamsQS(`${imwsApi}/iHCRSStatisticsController/gatherRetrospect`, params)
}
// 安全态势总览
// 安全运行评分-隐患
export function GetDangerRunScore(params) {
  return http.requestPost(`${idpsApi}/logistics/dangerController/getRunScore`, params)
}
// 隐患状态分析
export function GetStateStatistics(params) {
  return http.requestPost(`${idpsApi}/logistics/dangerController/getStateStatistics`, params)
}
// 隐患数量走势
export function GetHiddenDangerNumberAnalysis(params) {
  return http.requestPost(`${idpsApi}/logistics/dangerController/getHiddenDangerNumberAnalysis`, params)
}
// 隐患巡检任务分析
export function GetTaskAnalysis(params) {
  return http.postFormData(`${idpsApi}/planTaskNew/onePersonTaskQuantity`, params)
}
// 部门隐患巡检分析
export function GetInspectionAnalysisList(params) {
  return http.postFormData(`${idpsApi}/planTaskNew/departmentTaskTimeDateList`, params)
}
// 隐患类型分析
export function GetHiddenDangerTypeAnalysis(params) {
  return http.postRequest(`${idpsApi}/logistics/dangerController/getHiddenDangerTypeAnalysis`, {
    ...params
  })
}
// 隐患清单详情
export function GetHistoryFollowInfo(params) {
  return http.postRequest(`${idpsApi}/logistics/dangerController/getHistoryFollowInfo`, {
    ...params
  })
}
// 风险清单详情
export function GetRiskDetail(params) {
  return http.postRequest(`${idpsApi}/logistics/riskController/getRiskDetail`, {
    ...params
  })
}
// 巡检点分析
export function getTaskPointTimeDateList(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/taskPointTimeDateList`, params)
}
// 巡检任务分析
export function getOnePersonTaskQuantity(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/onePersonTaskQuantity`, params)
}
// 部门任务分析
export function getDepartmentTaskTimeDateList(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/departmentTaskTimeDateList`, params)
}
// 综合巡检-查询巡检点
export function getSpaceListData(params) {
  return http.postParamsQS(`${idpsApi}/taskPointRelease/spaceListData`, params)
}
// 安全运行评分-风险
export function GetRiskRunScore(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRunScore`, {
    ...params
  })
}

// 风险等级分析
export function GetRiskLevelStatistics(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRiskLevelStatistics`, {
    ...params
  })
}
// 风险类型分析
export function GetRiskTypeStatistics(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRiskTypeStatistics`, {
    ...params
  })
}
// 风险巡查任务分析
export function GetInspectionTaskAnalysis(params) {
  return http.postParamsRequest(`${idpsApi}/planTaskNew/onePersonTaskQuantity`, {
    ...params
  })
}
// 风险告知
export function getRiskInform(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRiskInform`, {
    ...params
  })
}
// 科室风险分布
export function GetRiskDeptDistribution(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRiskDeptStatistics`, {
    ...params
  })
}
// 建筑风险分布
export function GetRiskSpaceDistribution(params) {
  return http.requestPost(`${idpsApi}/logistics/riskController/getRiskNumberStatistics`, {
    ...params
  })
}
// 隐患清单列表
export function GetHiddenDangersList(params) {
  return http.postRequest(`${idpsApi}/logistics/dangerController/selectHiddenDangerList`, {
    ...params
  })
}
// 风险清单列表
export function GetRiskPageList(params) {
  return http.postRequest(`${idpsApi}/logistics/riskController/getRiskPageList`, {
    ...params
  })
}
// 隐患统计（总览）
export function GetDangerStatistics(params) {
  return http.postParamsQS(`${idpsApi}/riskWorkOrder/getWorkOrderStatisticsHomePage`, {
    // return http.postRequest(`${idpsApi}/logistics/dangerController/getHospitalQuestionStateAnalysis`, {
    ...params
  })
}
// 隐患空间下级数量/建筑
export function GetDangerSubordinateStatistics(params) {
  return http.postRequest(`${idpsApi}/logistics/dangerController/getJuniorDangerNumberStatistics`, {
    ...params
  })
}
// 隐患分析/部门
export function GetDeptDangerStatistics(params) {
  return http.postRequest(`${idpsApi}/logistics/dangerController/getDeptDangerStatistics`, {
    ...params
  })
}
// 综合巡检-根据空间查询巡检点数据
export function getTaskPointSpaceList(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/taskPointSpaceList`, params)
}
// 综合巡检-根据空间查询巡检点数据(ipsm)
export function getIPASpointDetail(params) {
  return http.postParamsQS(`${idpsApi}/taskPointRelease/detail`, params)
}
// 综合巡检-根据空间查询巡检点数据(insp)
export function getInspPointDetail(params) {
  return http.postParamsQS(`${inspApi}/taskPointRelease/detail`, params)
}
// 巡检列表(新巡检)
export function getInspectionData(params) {
  return http.postParamsQS(`${inspApi}/planTaskNew/getTaskListData`, params)
}
// 巡检列表(双预防)
export function getIpsmTaskListData(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/getTaskListData`, params)
}
// 获取照明统计
export function GetLightingStatistics(params) {
  return http.requestPost(`${iemcApi}/lightingStatistics/selectLighting`, {
    ...params
  })
}
// 获取建筑列表
export function GetHospitalWideList(params) {
  return http.requestPost(`${iemcApi}/lightingStatistics/selectLightingGroupByConstructName`, {
    ...params
  })
}
// 根据建筑统计不同楼层照明回路
export function GetFloorLightingList(params) {
  return http.requestPost(`${iemcApi}/lightingStatistics/selectLightingByConstruction`, {
    ...params
  })
}
// 根据建筑获取空间照明回路
export function GetLightingByConstruction(params) {
  return http.requestPost(`${iemcApi}/lightingStatistics/lightingGroupByConstructionId`, {
    ...params
  })
}
// 根据楼层获取空间照明回路
export function GetLightingByFloor(params) {
  return http.requestPost(`${iemcApi}/lightingStatistics/selectLightingByFloorIdOrSpaceId`, {
    ...params
  })
}
// 开关控制
export function LightOpenOrClose(params) {
  return http.requestPost(`${iemcApi}/lightingOperationMonitoring/openOrClose`, {
    ...params
  })
}
// iemc websocket
export function IemcWebsocket(params) {
  return http.websocketService(`${iemcWsApi}/imserver/${params}`)
}
// 巡检任务列表（根据部门）
export function getDeptTaskList(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/listData`, params)
}
// 部门列表
export function getDeptData(params) {
  return http.postRequest(`${idpsApi}/controlGroupInfo/getControlGroupInfoList`, params)
}
// 空间区域
export function getGridListData(params) {
  return http.postParamsQS(`${idpsApi}/riskController/getGridList`, params)
}
// 设备资产全局
export function getAssetListData(params) {
  return http.postParamsQS(`${inspApi}/asset/assetDetails/getAssetList`, params)
}
// 设备资产-根据设备过滤
export function getAssetDetails(params) {
  return http.postParamsQS(`${inspApi}/asset/assetDetails/getAssetDetails`, params)
}

// 获取设备分类列表
export function getDeviceType(params) {
  return http.postParamsQS(`${inspApi}/devicetype/selectDeviceClassifyList`, params)
}
// 获取报警记录
export function GetAlarmRecord(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectAlarmRecordPageByObjectId`, {
    ...params
  })
}
// 获取监测参数历史
export function GetParamDataHistoryList(params) {
  return http.requestPost(`${iemcApi}/client/getParamDataHistoryList`, {
    ...params
  })
}
// 获取传感器及参数列表
export function GetSelectBySurveyCode(params) {
  return http.getRequest(`${iemcApi}/client/getSelectBySurveyCode`, {
    ...params
  })
}
// 获取指定监测实体下的历史数据(图表)
export function GetChartData(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getChartData`, {
    ...params
  })
}
// 根据空间查询通行记录
export function GetAccessRecordBySpaceId(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryAccessRecordBySpaceId`, {
    ...params
  })
}
// 根据设备查询通行记录
export function GetAccessRecordByAassertNum(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryAccessRecordByAassertNum`, {
    ...params
  })
}
// 根据设备分页查询通行记录列表
export function GetAccessRecordList(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryAccessRecordList`, {
    ...params
  })
}
// 根据设备分页查询通行记录列表
export function GetAccessRecordListByPage(params) {
  return http.requestPost(`${iemcApi}/GasMonitor/queryAccessRecordListByPage`, {
    ...params
  })
}
export function getDutyPersonList(params) {
  return http.requestPost(`${ICIS_API}/clientDutyHandOver/getDutyPersonList`, params)
}
export function taskRepair(params) {
  return http.postParamsQS(`${iomsApi}/appOlgTaskManagement.do?saveTaskByWeChat`, params)
}
export function inspectionSubmit(params) {
  return http.postParamsQS(`${inspApi}/appWorkSubmission/submissionQualified`, params)
}
export function getInspPointVideoDetail(params) {
  return http.postParamsQS(`${inspApi}/taskPointRelease/appDetail`, params)
}
