<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">{{ dialogData.title }}</span>
        <!-- <el-tabs class="title-tabs" v-model="activeSpaceTabs" @tab-click="handleSpaceClick" v-if="dialogData.type === 'space'">
          <el-tab-pane label="空间台账" name="account"></el-tab-pane>
          <el-tab-pane label="空间信息变更记录" name="record"></el-tab-pane>
        </el-tabs> -->
      </template>
      <div class="dialog-content">
        <!-- 空间 -->
        <el-tabs
          v-if="dialogData.type === 'space'"
          v-model="activeSpaceTabs"
          class="title-tabs"
          @tab-click="handleSpaceClick"
        >
          <el-tab-pane label="空间台账" name="account"></el-tab-pane>
          <el-tab-pane label="空间信息变更记录" name="record"></el-tab-pane>
        </el-tabs>
        <div
          v-if="dialogData.type === 'ioms' || dialogData.type === 'imws' || dialogData.type === 'danger'"
          class="statistics-top"
        >
          <div v-for="(item, index) in statisticsData" :key="index">
            <p class="green-font">{{ item.value + (item?.unit ?? "") }}</p>
            <p>{{ item.name }}</p>
          </div>
        </div>
        <div v-if="dialogData.type === 'ioms'" class="search-box">
          <div>
            <span>工单号：</span>
            <el-input
              v-model="iomsSearchParams.workNum"
              placeholder="请输入工单号"
            ></el-input>
          </div>
          <div style="margin-left: 16px">
            <span class="transY">开单日期：</span>
            <el-date-picker
              v-model="kdDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              popper-class="date-style"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="{
                firstDayOfWeek: 1,
              }"
            >
            </el-date-picker>
          </div>
          <div style="margin-left: 16px">
            <span>所属科室：</span>
            <el-select
              v-model="iomsSearchParams.sourcesDept"
              placeholder="全部"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in sourcesDeptOptions"
                :key="item.id"
                :label="item.officeName"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-left: 16px">
            <span>工单类型：</span>
            <el-select
              v-model="iomsSearchParams.workTypeCode"
              placeholder="全部"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in workOrderTypeList"
                :key="item.workTypeName"
                :label="item.workTypeName"
                :value="item.workTypeCode"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetSearch">重置</el-button>
            <el-button @click="handleSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'risk'" class="search-box">
          <div class="riskInput">
            <span>风险点名称：</span>
            <el-input
              v-model="riskSearchParams.riskName"
              placeholder="风险点名称"
            ></el-input>
          </div>
          <div class="riskInput">
            <span>风险位置：</span>
            <el-cascader
              v-model="riskSearchParams.placeIds"
              :props="{
                label: 'gridName',
                value: 'id',
                checkStrictly: true,
              }"
              clearable
              :show-all-levels="false"
              :filterable="true"
              :options="ipsmSpaceData"
            >
            </el-cascader>
          </div>
          <div class="riskInput">
            <span>风险等级：</span>
            <el-select
              v-model="riskSearchParams.riskLevel"
              placeholder="全部"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in menuRiskOption"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="riskInput">
            <span>责任部门：</span>
            <el-select
              v-model="riskSearchParams.deptCode"
              placeholder="责任部门"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in ipsmDeptData"
                :key="item.id"
                :label="item.teamName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="riskInput">
            <span>责任人：</span>
            <el-input
              v-model="riskSearchParams.responsiblePersonName"
              placeholder="责任人"
            ></el-input>
          </div>
          <div class="search-btn">
            <el-button @click="resetRiskSearch">重置</el-button>
            <el-button @click="handleRiskSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'task'" class="search-box">
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.planName"
              placeholder="请输入计划名称"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.departmentName"
              placeholder="请输入小组"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.planPersonName"
              placeholder="请输入巡检人员"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-select
              v-model="taskSearchParams.taskStatus"
              placeholder="全部"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in statusList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-date-picker
              v-model="taskDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              popper-class="date-style"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
          <div class="search-btn">
            <el-button @click="resetTaskSearch">重置</el-button>
            <el-button @click="handleTaskSearch">查询</el-button>
          </div>
        </div>
        <div
          v-if="dialogData.type === 'icis' || dialogData.type === 'upkeep'"
          class="search-box"
        >
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.planName"
              placeholder="请输入计划名称"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.departmentName"
              placeholder="请输入小组"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="taskSearchParams.planPersonName"
              placeholder="请输入巡检人员"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-select
              v-model="taskSearchParams.taskStatus"
              placeholder="全部"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in statusList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-date-picker
              v-model="taskDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              popper-class="date-style"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
          <div class="search-btn">
            <el-button @click="resetInspTaskSearch">重置</el-button>
            <el-button @click="handleInspTaskSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'assets'" class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>设备名称：</span>
            <el-input
              v-model="assetSearchParams.assetsName"
              placeholder="请输入设备名称"
            ></el-input>
          </div>
          <div class="search-btn">
            <el-button @click="resetAssetSearch">重置</el-button>
            <el-button @click="handleAssetSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'inspAsset'" class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>设备名称：</span>
            <el-input
              v-model="inspAssetSearchParams.assetName"
              placeholder="请输入设备名称"
            ></el-input>
          </div>
          <div class="search-btn">
            <el-button @click="resetInspAssetSearch">重置</el-button>
            <el-button @click="handleInspAssetSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'entity'" class="search-box">
          <div style="margin-right: 10px" class="deviceInput">
            <!-- <span>设备名称：</span> -->
            <el-input
              v-model="entitySearchParams.assetName"
              placeholder="请输入资产名称"
            ></el-input>
          </div>
          <div style="margin-right: 10px" class="deviceInput">
            <el-select
              v-model="entitySearchParams.assetCategoryCode"
              filterable
              placeholder="请选择设备品类"
              @change="onMajorType"
            >
              <el-option
                v-for="item in majorList"
                :key="item.id"
                :label="item.baseName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 10px" class="deviceInput">
            <el-cascader
              v-model="entitySearchParams.assetTypeId"
              :props="{
                children: 'children',
                label: 'baseName',
                value: 'id',
                checkStrictly: true,
                multiple: false,
              }"
              :options="systemCodeList"
              :collapse-tags="true"
              placeholder="系统类别"
            ></el-cascader>
          </div>
          <div style="margin-right: 10px" class="deviceInput">
            <!-- <span>设备状态：</span> -->
            <el-select
              v-model="entitySearchParams.deviceStatus"
              placeholder="请选择运行状态"
              filterable
              clearable
              popper-class="new-select"
            >
              <el-option
                v-for="item in flowList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 10px" class="deviceInput">
            <el-cascader
              v-model="entitySearchParams.spaceId"
              placeholder="请选择设备位置"
              :props="{ label: 'ssmName', value: 'id', checkStrictly: true }"
              clearable
              :show-all-levels="false"
              :filterable="true"
              :options="deviceSpaces"
            >
            </el-cascader>
          </div>
          <!-- <div style="margin-right: 16px" class="assetsInput">
            <span>参数名称：</span>
            <el-input v-model="entitySearchParams.parameterName" placeholder="请输入监测实体"></el-input>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <span>传感器名称：</span>
            <el-input v-model="entitySearchParams.harvesterName" placeholder="请输入监测实体"></el-input>
          </div> -->
          <div class="search-btn">
            <el-button @click="resetEntitySearch">重置</el-button>
            <el-button @click="handleEntitySearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'fireproof'" class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>设备名称：</span>
            <el-input
              v-model="entitySearchParams.surveyName"
              placeholder="请输入设备名称"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <span>设备类型：</span>
            <el-select
              v-model="entitySearchParams.deviceStatus"
              placeholder="请选择类型"
              filterable
              clearable
              popper-class="new-select"
            >
              <el-option
                v-for="item in flowList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetFireproofEqu">重置</el-button>
            <el-button @click="searchFireproofEqu">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'light'" class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>回路名称：</span>
            <el-input
              v-model="lightSearchParams.surveryName"
              placeholder="请输入回路名称"
            ></el-input>
          </div>
          <div class="search-btn">
            <el-button @click="resetLightSearch">重置</el-button>
            <el-button @click="handleLightSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'parking'" class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <el-select
              v-model="parkingSearchParams.timeType"
              placeholder="时间范围"
              clearable
            >
              <el-option
                v-for="item in timeTypeList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <el-date-picker
              v-model="parkingSearchParams.dataRange"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <el-input
              v-model="parkingSearchParams.voucherNo"
              placeholder="凭证号"
              clearable
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <el-input
              v-model="parkingSearchParams.userName"
              placeholder="用户名称"
              clearable
            ></el-input>
          </div>
          <div class="search-btn">
            <el-button @click="resetParkingtSearch">重置</el-button>
            <el-button @click="handleParkingSearch">查询</el-button>
          </div>
        </div>
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :height="dialogData.height"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="selectConfigRowData"
          @row-click="tableRowClick"
        >
          <el-table-column
            v-for="(column, index) in tableColumn"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            :min-width="column.minWidth"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <table-render
                v-if="column.render"
                :sc="scope"
                :row="scope.row"
                :render="column.render"
              ></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
    <template v-if="retrospectShow">
      <retrospect
        ref="retrospect"
        :dialogShow="retrospectShow"
        :detailId="detailId"
        @retrospectCloseDialog="retrospectCloseDialog"
      ></retrospect>
    </template>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog
        v-dialogDrag
        :modal="false"
        :visible.sync="workOrderDetailCenterShow"
        custom-class="detailDialog main"
        :close-on-click-modal="false"
        :before-close="workOrderDetailCloseDialog"
      >
        <template slot="title">
          <span class="dialog-title"
          >综合维修（{{ iomsDetailObj.flowtype }}）</span
          >
        </template>
        <workOrderDetailList :rowData="iomsDetailObj" />
      </el-dialog>
    </template>
    <template v-if="hiddenDangerDetailsListShow">
      <el-dialog
        v-dialogDrag
        :modal="false"
        :visible.sync="hiddenDangerDetailsListShow"
        custom-class="detailDialog main"
        :close-on-click-modal="false"
        :before-close="hiddenDangerDetailCloseDialog"
      >
        <template slot="title">
          <span class="dialog-title">隐患详情</span>
        </template>
        <hiddenDangerDetailsList :rowData="dangerDetailObj" />
      </el-dialog>
    </template>
    <template v-if="riskDetailShow"> </template>
    <riskInspection
      v-if="dialogData.type === 'risk'"
      ref="riskInspection"
      :rowData="riskDetailObj"
    ></riskInspection>
    <inspectionDetail
      v-if="dialogData.type === 'icis' || dialogData.type === 'upkeep' || dialogData.type === 'task'"
      ref="inspectionDetail"
      :systemType="dialogData.type === 'icis' ? 'xjrw' : 'byrw'"
      :taskType="(dialogData.type === 'icis' || dialogData.type === 'upkeep') ? 'insp' : 'ipsm'"
      :dataInfo="taskkDetailObj"
      @closeDialog="closeInspPointDialog"
    ></inspectionDetail>
    <!-- <el-image style="width: 70%; height: 90%" :preview-src-list="[signaturePreview]"></el-image> -->
    <el-image-viewer
      v-if="showPreview"
      class="preview-image"
      :urlList="[signaturePreview]"
      :on-close="() => (showPreview = false)"
    ></el-image-viewer>
    <deviceDetails
      v-if="isDeviceDetails"
      :dialogShow="isDeviceDetails"
      :deviceData="deviceData"
      @deviceDetailsClose="() => (isDeviceDetails = false)"
    />
  </div>
</template>
<script lang="jsx">
import { GetHiddenDangersList, getDepartMedicalWasteList, GetRiskPageList, getDeptData, getGridListData, getIpsmTaskListData, getInspectionData, getDeviceType } from '@/utils/centerScreenApi'
import {
  getSpaceList,
  GetWorkOrderListBySpaceRuiAn,
  GerReckonCount,
  getModifyHistoryList,
  getMedicalWasteCountInfoByRuiAn,
  getGasList,
  getInspAssetDetails,
  getGroupOperationMonitoring
} from '@/utils/spaceManage'
import {
  getInParkingRecord,
  getOutParkingRecord
} from '@/utils/parking'
import { getAllOffice, getStructureTree } from '@/utils/peaceLeftScreenApi'
import { getAssetDetails } from '@/utils/centerScreenApi'
import { iomsWorkOrderParams } from '@/assets/common/dict.js'
import tableRender from './tableRender.vue'
import retrospect from '../../centerScreen/businessManagement/component/retrospect.vue'
import workOrderDetailList from '@views/normalMode/rightScreen/components/workOrderDetailList.vue'
import hiddenDangerDetailsList from '@views/centerScreen/safetyOverview/components/hiddenDangerDetailsList'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import icon_5 from '@/assets/images/icon-5.png'
import icon_3 from '@/assets/images/icon-3.png'
import icon_7 from '@/assets/images/icon-7.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import riskInspection from '../sysTypeComponent/components/riskInspection.vue'
import inspectionDetail from '../sysTypeComponent/components/inspectionDetail.vue'
import { monitorTypeList, hasModelCodeFilterProjectName } from '@/assets/common/dict.js'
import deviceDetails from './deviceDetails.vue'
export default {
  name: 'allTableComponentList',
  components: {
    'table-render': tableRender,
    retrospect,
    workOrderDetailList,
    hiddenDangerDetailsList,
    ElImageViewer,
    riskInspection,
    inspectionDetail,
    deviceDetails
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: '服务工单台账',
          type: 'ioms',
          height: 'calc(100% - 40px)'
        }
      }
    },
    assetsList: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      icon_5,
      icon_3,
      icon_7,
      icon_6,
      icon_2,
      // 医废
      detailId: '',
      retrospectShow: false,
      showPreview: false,
      signaturePreview: '',
      // 一站式
      iomsDetailObj: {},
      workOrderDetailCenterShow: false,
      iomsWorkOrderParams: iomsWorkOrderParams,
      // 隐患
      dangerDetailObj: {},
      hiddenDangerDetailsListShow: false,
      // 空间
      activeSpaceTabs: 'account',
      // 列表
      tableData: [],
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      // 列表字段
      tableColumn: [],
      iomsTableColumn: [
        {
          prop: 'workNum',
          label: '工单号'
        },
        {
          prop: 'createDate',
          label: '开单时间'
        },
        {
          prop: 'sourcesDeptName',
          label: '所属科室',
          formatter: (row) => {
            return row.sourcesDeptName === 'undefined' ? '' : row.sourcesDeptName
          }
        },
        {
          prop: 'localtionName',
          label: '服务地点'
        },
        {
          prop: 'itemServiceName',
          label: '服务事项'
        },
        {
          prop: 'workTypeName',
          label: '申报类型'
        },
        {
          prop: 'designatePersonName',
          label: '服务人员'
        },
        {
          prop: 'designatePersonPhone',
          label: '联系方式'
        },
        {
          prop: 'questionDescription',
          label: '说明'
        },
        {
          prop: 'flowtype',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.flowcode == '5' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '3' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_3} />
                    <span style="color:#3CC1FF">{row.row.flowtype}</span>
                  </div>
                )}
                {(row.row.flowcode == '7' || row.row.flowcode == '1' || row.row.flowcode == '4') && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_7} />
                    <span style="color:#D25F00">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '2' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode != '1' &&
                  row.row.flowcode != '2' &&
                  row.row.flowcode != '3' &&
                  row.row.flowcode != '4' &&
                  row.row.flowcode != '5' &&
                  row.row.flowcode != '6' &&
                  row.row.flowcode != '7' && (
                  <div class="status-box">
                    <span>{row.row.flowtype}</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            row.row.type = 'ioms'
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      imwsTableColumn: [
        {
          prop: 'officeName',
          label: '所属科室'
        },
        {
          prop: 'count',
          label: '医废数量'
        },
        {
          prop: 'barCode',
          label: '医废编码'
        },
        {
          prop: 'wasteType',
          label: '医废类型'
        },
        {
          prop: 'gatherTime',
          label: '收集时间'
        },
        {
          prop: 'gatherWeigh',
          label: '收集重量',
          formatter: (row) => {
            return row.gatherWeigh + 'Kg'
          }
        },
        {
          prop: 'receivedPersonName',
          label: '收集人员'
        },
        {
          prop: 'receivedSignature',
          label: '收集人员签字',
          render: (h, row) => {
            if (!row.row.receivedSignature || row.row.receivedSignature === undefined) {
              return <span></span>
            } else {
              return (
                <div class="operationBtn">
                  <span onClick={() => this.previewSignature(row.row.ossFilePrefix + row.row.receivedSignature)}>点击查看</span>
                </div>
              )
              // return <el-image style="width: 70%; height: 90%" preview-src-list={[row.row.ossFilePrefix + row.row.receivedSignature]}></el-image>
            }
          }
        },
        {
          prop: 'officeSignature',
          label: '科室人员签字',
          render: (h, row) => {
            if (!row.row.officeSignature || row.row.officeSignature === undefined) {
              return <span></span>
            } else {
              return (
                <div class="operationBtn">
                  <span onClick={() => this.previewSignature(row.row.ossFilePrefix + row.row.officeSignature)}>点击查看</span>
                </div>
              )
              // return <el-image style="width: 70%; height: 90%" preview-src-list={[row.row.ossFilePrefix + row.row.officeSignature]}></el-image>
            }
          }
        },
        {
          prop: 'inventoryStatus',
          label: '状态',
          formatter: (row) => {
            const data = ['已收集', '已入站', '已出站']
            if (row.inventoryStatus === '1') {
              return data[row.inventoryStatus + 1]
            }
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            row.row.type = 'imws'
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>追溯</span>
              </div>
            )
          }
        }
      ],
      dangerTableColumn: [
        {
          prop: 'questionCode',
          label: '隐患单号'
        },
        {
          prop: 'flowType',
          label: '隐患状态'
        },
        {
          prop: 'createByDeptName',
          label: '登记部门'
        },
        {
          prop: 'createTime',
          label: '登记时间'
        },
        {
          prop: 'createPersonName',
          label: '登记人'
        },
        {
          prop: 'riskName',
          label: '隐患等级'
        },
        {
          prop: 'rectificationPlanTime',
          label: '要求整改完成时间'
        },
        {
          prop: 'dutyDeptName',
          label: '责任部门'
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            row.row.type = 'danger'
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      spaceTableColumn: [
        // {
        //   prop: 'modelCode',
        //   label: '模型编码'
        // },
        {
          prop: 'roomCode',
          label: '房间编号'
        },
        {
          prop: 'localSpaceName',
          label: '空间名称'
        },
        {
          prop: 'buildName',
          label: '所属建筑'
        },
        {
          prop: 'floorName',
          label: '所属楼层'
        },
        {
          prop: 'functionDictName',
          label: '功能类型'
        },
        {
          prop: 'dmName',
          label: '归属部门'
        },
        // {
        //   prop: 'principalName',
        //   label: '空间责任人'
        // },
        {
          prop: 'area',
          label: '建筑面积(m²)',
          minWidth: '100'
        },
        {
          prop: 'useArea',
          label: '使用面积(m²)',
          minWidth: '100'
        },
        {
          prop: 'publicArea',
          label: '公区面积（m²）',
          minWidth: '100',
          formatter: (row) => {
            return row.area - row.useArea
          }
        }
      ],
      riskTableColumn: [
        {
          prop: 'riskName',
          label: '风险点名称'
        },
        {
          prop: 'riskPlace',
          label: '风险位置'
        },
        {
          prop: 'riskLevelName',
          label: '风险等级'
        },
        {
          prop: 'taskTeamName',
          label: '责任部门'
        },
        {
          prop: 'responsiblePersonName',
          label: '责任人'
        },
        {
          prop: 'urgentContactPhone',
          label: '应急电话'
        },
        {
          prop: '',
          label: '操作',
          render: (h, row) => {
            row.row.type = 'risk'
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      taskTableColumn: [
        {
          prop: 'taskName',
          label: '任务名称'
        },
        // {
        //   prop: 'planTypeName',
        //   label: '计划类型'
        // },
        // {
        //   prop: 'planName',
        //   label: '计划名称'
        // },
        {
          prop: 'taskName',
          label: '周期类型',
          render: (h, row) => {
            return <span>{this.cycleTypeFn(row.row)}</span>
          }
        },
        {
          prop: 'taskStartTime',
          label: '应巡时间'
        },
        {
          prop: 'taskEndTime',
          label: '应巡日期'
        },
        {
          prop: 'planPersonName',
          label: '巡检小组/人员',
          render: (h, row) => {
            return <span>{row.row.planPersonName || row.row.distributionTeamName}</span>
          }
        },
        {
          prop: '',
          label: '操作',
          render: (h, row) => {
            if (this.dialogData.type === 'icis' || this.dialogData.type === 'upkeep') {
              row.row.type = 'insp'
            } else {
              row.row.type = 'ipsm'
            }
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      // 资产table数据
      assetsTableColumn: [
        {
          prop: 'assetName',
          label: '资产名称'
        },
        {
          prop: 'assetCode',
          label: '资产编码'
        },
        {
          prop: 'assetBrand',
          label: '品牌'
        },
        {
          prop: 'assetModel',
          label: '型号'
        },
        {
          prop: 'regionName',
          label: '所在区域'
        },
        {
          prop: 'professionalCategoryName',
          label: '专业类别'
        },
        {
          prop: 'updateTime',
          label: '更新时间'
        }
      ],
      // 实体table数据
      entityTableColumn: [
        {
          prop: 'assetName',
          label: '关联资产',
          formatter: (row) => {
            return row.assetName || '-'
          }
        },
        {
          prop: 'assetCategoryName',
          label: '资产类型',
          formatter: (row) => {
            return row.assetCategoryName || '-'
          }
        },
        {
          prop: 'assetTypeName',
          label: '设备类型',
          formatter: (row) => {
            return row.assetTypeName || '-'
          }
        },
        {
          prop: 'surveyName',
          label: '设备名称'
        },
        {
          prop: 'regionName',
          label: '设备位置',
          formatter: (row) => {
            return row.regionName || '-'
          }
        },
        {
          prop: 'menuName',
          label: '归属系统'
        },
        {
          prop: 'status',
          label: '运行状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">异常</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: '',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span onClick={(e) => { this.deviceAssetDetails(row.row), e.stopPropagation() }}>查看</span>
              </div>
            )
          }
        }
      ],
      // 照明台账
      lightyTableColumn: [
        {
          prop: 'loopsName',
          label: '回路名称'
        },
        {
          prop: 'entityTypeName',
          label: '归属系统'
        },
        {
          prop: 'status',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.outputStatus == '0' && (
                  <div class="table-icon">
                    <span style="color:#FF2D55">关闭</span>
                  </div>
                )}
                {row.row.outputStatus == '1' && (
                  <div class="table-icon">
                    <span style="color:#61E29D">开启</span>
                  </div>
                )}
              </div>
            )
          }
        }
        // {
        //   prop: '',
        //   label: '操作',
        //   render: (h, row) => {
        //     return (
        //       <div class="operationBtn">
        //         {row.row.outputStatus == '0' && (
        //           <div class="table-icon" onClick={() => this.selectConfigRowData(row.row, '1')}>
        //             开启
        //           </div>
        //         )}
        //         {row.row.outputStatus == '1' && (
        //           <div class="table-icon" onClick={() => this.selectConfigRowData(row.row, '0')}>
        //             关闭
        //           </div>
        //         )}
        //       </div>
        //     )
        //   }
        // }
      ],
      lightSearchParams: {
        surveryName: ''
      },
      // 停车场
      parkingSearchParams: {
        timeType: '', // 时间
        voucherNo: '', // 凭证号
        userName: '', // 用户名称
        dataRange: [] // 时间范围
      },
      parkingInTableColumn: [
        {
          prop: 'proofNum',
          label: '凭证号',
          minWidth: '100'
        },
        {
          prop: 'userName',
          label: '用户名称',
          minWidth: '100'
        },
        {
          prop: 'setMealType',
          label: '套餐类型',
          minWidth: '100'
        },
        {
          prop: 'recordType',
          label: '记录类型',
          minWidth: '120'
        },
        {
          prop: 'inParkingTime',
          label: '入场时间',
          minWidth: '160'
        },
        {
          prop: 'inParkingEq',
          label: '入场设备',
          minWidth: '100'
        },
        {
          prop: '',
          label: '视频图片',
          minWidth: '100',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #5482ee" onClick={() => this.handleDetailEvent(row.row)}>
                  查看
                </span>
              </div>
            )
          }
        },
        {
          prop: 'whether',
          label: '是否出场',
          minWidth: '100'
        },
        {
          prop: 'parkingLotName',
          label: '车场名称',
          minWidth: '160'
        },
        {
          prop: 'operateTime',
          label: '操作时间',
          minWidth: '160'
        },
        {
          prop: 'operator',
          label: '操作员',
          minWidth: '100'
        },
        {
          prop: 'operationModel',
          label: '操作方式',
          minWidth: '100'
        }
      ],
      parkingOutTableColumn: [
        {
          prop: 'proofNum',
          label: '凭证号',
          minWidth: '100'
        },
        {
          prop: 'userName',
          label: '用户名称',
          minWidth: '100'
        },
        {
          prop: 'setMealType',
          label: '套餐类型',
          minWidth: '100'
        },
        {
          prop: 'recordType',
          label: '记录类型',
          minWidth: '120'
        },
        {
          prop: 'inParkingTime',
          label: '入场时间',
          minWidth: '160'
        },
        {
          prop: 'outParkingTime',
          label: '出场时间',
          minWidth: '160'
        },
        {
          prop: 'inParkingEq',
          label: '入场设备',
          minWidth: '100'
        },
        {
          prop: 'outParkingEq',
          label: '出场设备',
          minWidth: '100'
        },
        {
          prop: '',
          label: '视频图片',
          minWidth: '100',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #5482ee" onClick={() => this.handleDetailEvent(row.row)}>
                  查看
                </span>
              </div>
            )
          }
        },
        {
          prop: 'parkingPrice',
          label: '实收总金额',
          minWidth: '120'
        },
        {
          prop: 'parkingTime',
          label: '停车时长',
          minWidth: '100'
        },
        {
          prop: 'parkingLotName',
          label: '车场名称',
          minWidth: '160'
        },
        {
          prop: 'operateTime',
          label: '操作时间',
          minWidth: '160'
        },
        {
          prop: 'operator',
          label: '操作员',
          minWidth: '100'
        },
        {
          prop: 'operationModel',
          label: '操作方式',
          minWidth: '100'
        }
      ],
      flowList: [
        {
          label: '正常',
          value: '0'
        },
        {
          label: '异常',
          value: '10'
        },
        {
          label: '离线',
          value: '6'
        }
      ],
      // 统计数据
      statisticsData: [],
      statisticsIoms: [
        {
          name: '服务总量',
          value: 0,
          field: 'all'
        },
        {
          name: '已完工',
          value: 0,
          field: 'completed'
        },
        {
          name: '未完工',
          value: 0,
          field: 'unfinishedwork'
        },
        {
          name: '已取消',
          value: 0,
          field: 'cancelled'
        },
        {
          name: '完工率',
          value: 0,
          // unit: '%',
          field: 'completionRate'
        },
        {
          name: '平均响应',
          value: 0,
          // unit: '分钟',
          field: 'response'
        },
        {
          name: '平均完工',
          value: 0,
          // unit: '秒',
          field: 'finishTime'
        },
        {
          name: '返修率',
          value: 0,
          // unit: '%',
          field: 'repairWork'
        },
        {
          name: '综合评价',
          value: 0,
          // unit: '分',
          field: 'evaluate'
        },
        {
          name: '回访率',
          value: 0,
          // unit: '%',
          field: 'callBack'
        }
      ],
      statisticsImws: [
        {
          name: '今日收集',
          value: 0,
          unit: 'Kg',
          field: 'waitWeigh'
        },
        {
          name: '今日入库',
          value: 0,
          unit: 'Kg',
          field: 'stockInWeigh'
        },
        {
          name: '今日出库',
          value: 0,
          unit: 'Kg',
          field: 'issuedWeigh'
        },
        {
          name: '逾期未出库',
          value: 0,
          unit: 'Kg',
          field: 'timeoutWeigh'
        }
      ],
      statisticsDanger: [
        {
          name: '一般隐患',
          value: ''
        },
        {
          name: '重点隐患',
          value: ''
        }
      ],
      iomsSearchParams: {
        workNum: '',
        sourcesDept: '',
        workTypeCode: ''
      },
      kdDate: '',
      sourcesDeptOptions: [],
      workOrderTypeList: [
        {
          workTypeName: '全部工单',
          workTypeCode: ''
        },
        {
          workTypeName: '巡检报修',
          workTypeCode: '10'
        },
        {
          workTypeName: '后勤设备',
          workTypeCode: '17'
        },
        {
          workTypeName: '随手拍',
          workTypeCode: '11'
        },
        {
          workTypeName: '综合维修',
          workTypeCode: '1'
        },
        {
          workTypeName: '应急保洁',
          workTypeCode: '2'
        },
        {
          workTypeName: '运送服务',
          workTypeCode: '3'
        },
        {
          workTypeName: '订餐服务',
          workTypeCode: '4'
        },
        {
          workTypeName: '综合服务',
          workTypeCode: '6'
        },
        {
          workTypeName: '后勤投诉',
          workTypeCode: '5'
        },
        {
          workTypeName: '医疗设备',
          workTypeCode: '7'
        },
        {
          workTypeName: '巡检自修',
          workTypeCode: '8'
        },
        {
          workTypeName: '巡检整改',
          workTypeCode: '9'
        }
      ],
      riskSearchParams: {
        riskName: '',
        placeIds: '',
        riskLevel: '',
        deptCode: '',
        responsiblePersonName: ''
      },
      menuRiskOption: [
        {
          id: '',
          name: '全部风险'
        },
        {
          id: '1',
          name: '重大风险'
        },
        {
          id: '2',
          name: '较大风险'
        },
        {
          id: '3',
          name: '一般风险'
        },
        {
          id: '4',
          name: '低风险'
        }
      ],
      ipsmDeptData: [],
      ipsmSpaceData: [],
      deviceSpaces: [], // 空间位置
      riskDetailShow: false,
      taskDetailShow: false,
      riskDetailObj: {},
      taskDate: [],
      taskSearchParams: {
        planName: '',
        departmentName: '',
        planPersonName: '',
        taskStatus: ''
      },
      assetSearchParams: {
        assetsName: '' // 设备名称
      },
      inspAssetSearchParams: {
        assetName: '' // 设备名称
      },
      entitySearchParams: {
        assetName: '', // 监测实体
        deviceStatus: '',
        spaceId: [],
        assetCategoryCode: '',
        assetTypeId: []
        // parameterName: '', //监测参数
        // harvesterName: '' //传感器名称
      },
      statusList: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '',
          label: '全部'
        }
      ],
      timeTypeList: [
        {
          id: 1,
          label: '今天'
        },
        {
          id: 2,
          label: '昨天'
        },
        {
          id: 3,
          label: '上周'
        },
        {
          id: 4,
          label: '上个月'
        },
        {
          id: 5,
          label: '最近60天'
        }
      ],
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      taskkDetailObj: {},
      majorList: [], // 专业类别列表
      systemCodeList: [], // 系统类别
      isDeviceDetails: false,
      deviceData: {}
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getTableData('init')
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) {}
    this.getSourcesDeptOptions()
  },
  methods: {
    // 设备详情
    deviceAssetDetails(row) {
      this.deviceData = row
      this.isDeviceDetails = true
    },
    // 查询二级以下设备字典
    onMajorType(val) {
      this.entitySearchParams.assetTypeId = ''
      this.systemCodeList = []
      const data = {
        startLevel: '2',
        parentId: val,
        levelType: '5'
      }
      getDeviceType(data).then((res) => {
        if (res.data.code == '200') {
          this.systemCodeList = this.$tools.transData(res.data.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 获取设备分类列表
    getDeviceType() {
      getDeviceType({ levelType: 1 }).then((res) => {
        if (res.data.code == 200) {
          this.majorList = res.data.data
        }
      })
    },
    tableRowClick(row) {
      let params = {}
      // 受filterName包含的为使用modelcode跳转（关联模型），不是的为使用assetsid跳转（关联表计）
      const filterName = hasModelCodeFilterProjectName
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName)).map(v => v.projectCode)
      // 如果关联了设备即跳转设备详情页
      if (['entity', 'assets'].includes(this.dialogData.type)) {
        const device = row.modelCode ? this.assetsList.find((item) => item.modelCode === row.modelCode) : {}
        params = {
          DeviceCode: device?.modelCode,
          DeviceName: device?.assetName,
          assetsId: (row.assetId || row.assetsId) ? (row.assetId || row.assetsId) : device?.assetId,
          projectCode: row?.projectCode,
          spaceCode: row?.regionCode
        }
        if (filterMonitorList.includes(params.projectCode) || filterMonitorList.includes(this.dialogData.projectCode)) {
          if (params.assetsId) {
            if (params.DeviceCode) { // 设备
              try {
                window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
                this.closeDialog()
              } catch (error) {}
            } else {
              this.$message.warning('当前设备暂未录入模型编码!')
            }
          } else {
            this.$message.warning('当前设备暂未关联资产!')
          }
        } else {
          if (params.assetsId) { // 点位
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
              this.closeDialog()
            } catch (error) {}
          } else {
            this.$message.warning('当前设备暂未关联资产!')
          }
        }
      }
    },
    resetSearch() {
      this.iomsSearchParams = {
        workNum: '',
        sourcesDept: '',
        workTypeCode: ''
      }
      this.kdDate = ''
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        localtion: this.dialogData.localtion
      }
      let ssmCodeList = []
      let areaParams = {}
      ssmCodeList = this.dialogData?.ssmCodes?.split(',') ?? []
      areaParams = {
        region: ssmCodeList.at(-4) ?? '',
        buliding: ssmCodeList.at(-3) ?? '',
        storey: ssmCodeList.at(-2) ?? '',
        room: ssmCodeList.length == 5 ? ssmCodeList.at(-1) : ''
      }
      this.getWorkOrderTableData({ ...params, ...areaParams })
    },
    handleSearch() {
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        localtion: this.dialogData.localtion
      }
      let ssmCodeList = []
      let areaParams = {}
      ssmCodeList = this.dialogData?.ssmCodes?.split(',') ?? []
      areaParams = {
        region: ssmCodeList.at(-4) ?? '',
        buliding: ssmCodeList.at(-3) ?? '',
        storey: ssmCodeList.at(-2) ?? '',
        room: ssmCodeList.length == 5 ? ssmCodeList.at(-1) : ''
      }
      this.getWorkOrderTableData({ ...params, ...areaParams })
    },
    getSourcesDeptOptions() {
      getAllOffice().then((res) => {
        if (res.data.success) {
          this.sourcesDeptOptions = res.data.body.result
        }
      })
    },
    getTableData(type) {
      this.tableLoading = true
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        localtion: this.dialogData.localtion
      }
      let ssmCodeList = []
      let areaParams = {}
      switch (this.dialogData.type) {
        case 'ioms':
          ssmCodeList = this.dialogData?.ssmCodes?.split(',') ?? []
          areaParams = {
            region: ssmCodeList.at(-4) ?? '',
            buliding: ssmCodeList.at(-3) ?? '',
            storey: ssmCodeList.at(-2) ?? '',
            room: ssmCodeList.length == 5 ? ssmCodeList.at(-1) : '',
            deviceId: this.dialogData.deviceId
          }
          this.iomsWorkOrderParams = {...this.iomsWorkOrderParams, ...areaParams}
          if (type === 'init') {
            this.tableColumn = this.iomsTableColumn
            this.gerReckonCount(areaParams)
          }
          this.getWorkOrderTableData({ ...params, ...areaParams })
          break
        case 'imws':
          Object.assign(params, {
            dateType: 'all',
            spatialId: this.dialogData.localtion,
            ssmType: this.dialogData.ssmType,
            pageSize: 50
          })
          delete params.localtion
          if (type === 'init') {
            this.tableColumn = this.imwsTableColumn
            this.getIMWSstatisticsData(params)
          }
          this.getIMWSTableList(params)
          break
        case 'danger':
          if (type === 'init') {
            this.tableColumn = this.dangerTableColumn
            this.statisticsData = this.statisticsDanger
          }
          params.placeIds = this.dialogData.placeIds
          this.getDangerTableData(params)
          break
        case 'space':
          if (type === 'init') {
            this.tableColumn = this.spaceTableColumn
            this.tableLoading = false
          }
          this.getSpaceTableData()
          break
        case 'risk':
          if (type === 'init') {
            this.riskSearchParams.placeIds = this.dialogData?.ssmCodes?.split(',') ?? []
            this.getDeptListData()
            this.getGridListData()
            this.tableColumn = this.riskTableColumn
            this.tableLoading = false
          }
          this.getRiskTableData()
          break
        case 'task':
          if (type === 'init') {
            this.tableColumn = this.taskTableColumn
            this.tableLoading = false
          }
          this.getTaskListData()
          break
        case 'icis':
          if (type === 'init') {
            this.tableColumn = this.taskTableColumn
            this.tableLoading = false
          }
          this.getInspTaskListData()
          break
        case 'upkeep':
          if (type === 'init') {
            this.tableColumn = this.taskTableColumn.map((item) => {
              if (item.prop === 'taskStartTime') {
                item.label = '应保养时间'
              } else if (item.prop === 'taskEndTime') {
                item.label = '应保养日期'
              } else if (item.prop === 'planPersonName') {
                item.label = '保养小组/人员'
              }
              return item
            })

            this.tableLoading = false
          }
          this.getInspTaskListData()
          break
        case 'assets':
          if (type === 'init') {
            this.tableColumn = this.assetsTableColumn
            this.tableLoading = false
          }
          this.getAssetsTableData()
          break
        case 'entity':
          if (type === 'init') {
            this.getTreelist()
            this.getDeviceType()
            this.tableColumn = this.entityTableColumn
            this.tableLoading = false
            if (this.dialogData.isSpace == '0') {
              this.entitySearchParams.spaceId = [this.dialogData.spaceId]
            } else {
              this.entitySearchParams.menuCode = this.dialogData.menuCode
            }
          }
          this.getEntityTableData()
          break
        case 'inspAsset':
          if (type === 'init') {
            this.tableColumn = this.assetsTableColumn
            this.tableLoading = false
          }
          this.getInspAssetsListData()
          break
        case 'light':
          if (type === 'init') {
            this.tableColumn = this.lightyTableColumn
            this.tableLoading = false
          }
          this.getLightListData()
          break
        case 'parking':
          if (type === 'init') {
            const obj = this.dialogData
            if (obj.objType == 'in') {
              this.tableColumn = this.parkingInTableColumn
            } else {
              this.tableColumn = this.parkingOutTableColumn
            }
            this.tableLoading = false
          }
          this.getParkingListData()
          break
      }
    },
    // 获取一站式列表
    getWorkOrderTableData(params) {
      this.tableLoading = true
      GetWorkOrderListBySpaceRuiAn({ ...this.iomsWorkOrderParams, ...this.iomsSearchParams, sectionStartDate: this.kdDate ? this.kdDate[0] : '', sectionEndDate: this.kdDate ? this.kdDate[1] : '', ...params }).then(
        (res) => {
          const data = res.data
          this.tableLoading = false
          if (res.status === 200) {
            this.tableData = res.data.rows
            this.total = res.data.total
          } else {
            this.$message({
              message: data.message,
              type: 'warning'
            })
          }
        }
      )
    },
    gerReckonCount(params) {
      GerReckonCount({ ...this.iomsWorkOrderParams, ...params }).then((res) => {
        if (res.data.success && res.data.body.data) {
          const data = res.data.body.data
          this.statisticsIoms.forEach((item) => {
            item.value = data[item.field]
          })
          this.statisticsData = this.statisticsIoms
        }
      })
    },
    // 获取医废列表
    getIMWSTableList(params) {
      Object.assign(params, { dateType: 'all' })
      getDepartMedicalWasteList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.rows.length) {
          this.tableData = data.rows
          this.total = data.total
        } else {
          this.tableData = []
        }
      })
    },
    getIMWSstatisticsData(params) {
      getMedicalWasteCountInfoByRuiAn(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.statisticsImws.forEach((item) => {
            item.value = data.data.list[0][item.field]
          })
          this.statisticsData = this.statisticsImws
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 医废查看签字预览图
    previewSignature(url) {
      this.showPreview = true
      this.signaturePreview = url
    },
    // 获取隐患列表
    getDangerTableData(params) {
      const ssmCods = this.dialogData?.ssmCodes?.split(',') ?? []
      params.placeIds = ssmCods[ssmCods.length - 1]
      GetHiddenDangersList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.list
          this.total = data.data.sum
          this.statisticsDanger[0].value = data.data.dataMap ? data.data.dataMap.generalCount : 0
          this.statisticsDanger[1].value = data.data.dataMap ? data.data.dataMap.greatCount : 0
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取空间列表
    getSpaceTableData() {
      const params = {
        current: this.currentPage,
        size: this.pageSize,
        deptId: this.dialogData.deptId, // 部门id
        functionDictId: this.dialogData.functionDictId, // 空间类型id
        modelCode: this.dialogData.modelCode, // 空间模型编码
        spaceState: this.dialogData.spaceState, // 空间状态
        haveModel: 0
      }
      getSpaceList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === 200) {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取风险列表
    getRiskTableData() {
      const params = {
        pageSize: this.pageSize,
        pageNo: this.currentPage,
        placeIds: this.riskSearchParams.placeIds[this.riskSearchParams.placeIds.length - 1],
        riskName: this.riskSearchParams.riskName,
        riskLevel: this.riskSearchParams.riskLevel,
        deptCode: this.riskSearchParams.deptCode,
        responsiblePersonName: this.riskSearchParams.responsiblePersonName
      }
      GetRiskPageList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.sum
          this.tableLoading = false
        }
      })
    },
    // 获取资产设备
    getAssetsTableData() {
      const params = {
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        deviceIds: this.dialogData.deviceId,
        assetName: this.assetSearchParams.assetsName
      }
      getAssetDetails(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.assetDetailsList
          this.total = res.data.data.sum
          this.tableLoading = false
        }
      })
    },
    // 获取实体列表
    getEntityTableData() {
      let params = {}
      Object.assign(params, {
        pageSize: this.pageSize,
        page: this.currentPage,
        projectCode: this.dialogData.projectCode,
        entityTypeId: this.dialogData.entityTypeId,
        ...this.entitySearchParams
      })
      params.spaceId = params.spaceId[params.spaceId.length - 1]
      params.assetTypeId = params.assetTypeId[params.assetTypeId.length - 1]
      getGasList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.count
          this.tableLoading = false
        }
      })
    },
    // 获取照明列表
    getLightListData() {
      let ssmCodeList = []
      ssmCodeList = this.dialogData?.ssmCodes?.split(',') ?? []
      const params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        projectCode: this.dialogData.projectCode,
        type: 3,
        ...this.lightSearchParams
      }
      const obj = this.dialogData.objInfo
      if (obj) {
        if (this.dialogData.sortType == 'jz') {
          params.constructionId = obj.constructId ? obj.constructId : this.dialogData.ssmType < 4 ? '' : ssmCodeList.at(-1)
        } else if (this.dialogData.sortType == 'lc') {
          params.floorId = obj.floorId ? obj.floorId : this.dialogData.ssmType < 4 ? '' : ssmCodeList.at(-1)
        } else if (this.dialogData.sortType == 'mk') {
          params.actuatorId = obj.actuatorId
          params.constructionId = this.dialogData.ssmType < 4 ? '' : ssmCodeList.at(-1)
        } else if (this.dialogData.sortType == 'fwkj') {
          params.spaceTypeId = obj.spaceTypeId
          params.constructionId = this.dialogData.ssmType < 4 ? '' : ssmCodeList.at(-1)
        }
      }
      getGroupOperationMonitoring(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code == 200) {
          this.tableData = data.data.list
          this.total = data.data.count
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取出库入库列表
    getParkingListData() {
      const obj = this.dialogData
      if (obj.objType == 'in') {
        let params = {
          querySection: this.parkingSearchParams.timeType,
          sectionBegin: this.parkingSearchParams.dataRange[0] || '',
          sectionEnd: this.parkingSearchParams.dataRange[1] || '',
          userName: this.parkingSearchParams.userName,
          proofNum: this.parkingSearchParams.voucherNo,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }
        getInParkingRecord(params).then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code == 200) {
            data.data.forEach((i) => {
              i.whether = '否'
            })
            this.tableData = data.data
            this.total = data.total
          } else {
            this.$message({
              message: data.msg,
              type: 'warning'
            })
          }
        })
      } else {
        let params = {
          timeType: this.parkingSearchParams.timeType,
          sectionBegin: this.parkingSearchParams.dataRange[0] || '',
          sectionEnd: this.parkingSearchParams.dataRange[1] || '',
          userName: this.parkingSearchParams.userName,
          voucherNo: this.parkingSearchParams.voucherNo,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }
        getOutParkingRecord(params).then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code == 200) {
            this.tableData = data.data
            this.total = data.total
          } else {
            this.$message({
              message: data.msg,
              type: 'warning'
            })

          }
        })
      }
    },
    // 获取空间历史修改记录表
    getModifyHistoryTableData(modelCodes) {
      const params = {
        current: this.currentPage,
        size: this.pageSize,
        modelCodes: modelCodes.toString() // 空间模型编码
      }
      getModifyHistoryList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === 200) {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    handleSpaceClick() {
      Object.assign(this, {
        pageSize: 15,
        currentPage: 1,
        total: 0
      })
      if (this.activeSpaceTabs === 'record') {
        const modelCodeList = Array.from(this.tableData, ({ modelCode }) => modelCode)
        this.getModifyHistoryTableData(modelCodeList)
      } else {
        this.getSpaceTableData()
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData('page')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableData('page')
    },
    // 双击 以及操作点击事件
    selectConfigRowData(row) {
      switch (row.type) {
        case 'ioms':
          this.iomsDetailObj = row
          // Object.assign(this.iomsDetailObj, {
          //   compontentsSource: 'spaceManage'
          // })
          this.workOrderDetailCenterShow = true
          break
        case 'imws':
          this.detailId = row.id
          this.retrospectShow = true
          break
        case 'danger':
          this.dangerDetailObj = row
          this.hiddenDangerDetailsListShow = true
          break
        case 'risk':
          this.riskDetailShow = true
          this.riskDetailObj = row
          this.$refs.riskInspection.getRiskDetail(row.id)
          this.$refs.riskInspection.hiddenDangerDetailsListShow = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
          break
        case 'insp':
          this.taskDetailShow = true
          this.taskkDetailObj = row
          this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
          this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'insp')
          try {
            window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
          break
        case 'ipsm':
          this.taskDetailShow = true
          this.taskkDetailObj = row
          this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
          this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'ipsm')
          try {
            window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
          break
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    retrospectCloseDialog() {
      this.retrospectShow = false
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
    },
    hiddenDangerDetailCloseDialog() {
      this.hiddenDangerDetailsListShow = false
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
    },
    riskDetailCloseDialog() {
      this.riskDetailShow = false
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
    },
    closeInspPointDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 取消按钮
    closeDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.$emit('configCloseDialog')
    },
    resetRiskSearch() {
      this.riskSearchParams = {
        riskName: '',
        placeIds: this.dialogData?.ssmCodes?.split(',') ?? [],
        riskLevel: '',
        deptCode: '',
        responsiblePersonName: ''
      }
      this.pageSize = 15
      this.currentPage = 1
      this.getRiskTableData()
    },
    handleRiskSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getRiskTableData()
    },
    // 获取部门列表（ipsm）
    getDeptListData() {
      getDeptData({}).then((res) => {
        if (res.data.code == '200') {
          this.ipsmDeptData = res.data.data.list
        }
      })
    },
    // 获取空间网格（ipsm）
    getGridListData() {
      getGridListData().then((res) => {
        if (res.data.code == '200') {
          this.ipsmSpaceData = this.$tools.transData(res.data.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.deviceSpaces = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    resetTaskSearch() {
      this.taskDate = []
      this.pageSize = 15
      this.currentPage = 1
      this.taskSearchParams = {
        planName: '',
        departmentName: '',
        planPersonName: '',
        taskStatus: ''
      }
      this.getTaskListData()
    },
    handleTaskSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getTaskListData()
    },
    // 巡检保养查询重置
    resetInspTaskSearch() {
      this.taskDate = []
      this.pageSize = 15
      this.currentPage = 1
      this.taskSearchParams = {
        planName: '',
        departmentName: '',
        planPersonName: '',
        taskStatus: ''
      }
      this.getInspTaskListData()
    },
    handleInspTaskSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getInspTaskListData()
    },
    // 设备资产台账列表重置
    resetAssetSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.assetSearchParams = {
        assetsName: ''
      }
      this.getAssetsTableData()
    },

    // 设备资产台账列表查询
    handleAssetSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getAssetsTableData()
    },
    // 回路名称重置
    resetLightSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.lightSearchParams = {
        surveryName: ''
      }
      this.getLightListData()
    },
    // 回路名称查询
    handleLightSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getLightListData()
    },
    // 停车重置
    resetParkingtSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.parkingSearchParams = {
        timeType: '', // 时间
        voucherNo: '', // 凭证号
        userName: '', // 用户名称
        dataRange: [] // 时间范围: ''
      }
      this.getParkingListData()
    },
    // 停车查询
    handleParkingSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getParkingListData()
    },
    // Insp资产重置
    resetInspAssetSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.inspAssetSearchParams = {
        assetName: ''
      }
      this.getInspAssetsListData()
    },
    // insp查询
    handleInspAssetSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getInspAssetsListData()
    },
    // 实体列表重置
    resetEntitySearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.entitySearchParams = {
        assetName: '',
        deviceStatus: '',
        spaceId: [],
        assetCategoryCode: '',
        assetTypeId: []
        // parameterName: '',
        // harvesterName: ''
      }
      this.getEntityTableData()
    },
    // 实体查询
    handleEntitySearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getEntityTableData()
    },
    // 防火分区设备台账
    resetFireproofEqu() {},

    // 防火分区设备台账
    searchFireproofEqu() {},

    // 巡查列表
    getTaskListData() {
      const params = {
        ...this.taskSearchParams,
        pageSize: this.pageSize,
        pageNo: this.currentPage,
        spaceCode: this.dialogData.ssmCodes?.substring(this.dialogData.ssmCodes.lastIndexOf(',') + 1) ?? '',
        taskStartTime: this.taskDate[0],
        taskEndTime: this.taskDate[1],
        spaceLevel: this.dialogData.ssmType ? this.dialogData.ssmType - 1 : 5
      }
      getIpsmTaskListData(params).then((res) => {
        if (res.data.code == '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.sum.allCount
          this.tableLoading = false
        }
      })
    },
    // insp 巡检及保养
    getInspTaskListData() {
      const params = {
        ...this.taskSearchParams,
        distributionTeamName: this.taskSearchParams.departmentName,
        pageSize: this.pageSize,
        pageNo: this.currentPage,
        taskStartTime: this.taskDate[0],
        taskEndTime: this.taskDate[1],
        deviceId: this.dialogData.deviceId,
        dateType: this.dialogData.dateType,
        taskStatus: this.taskSearchParams.taskStatus ? this.taskSearchParams.taskStatus : this.dialogData.taskStatus,
        systemCode: this.dialogData.systemCode
      }
      getInspectionData(params).then((res) => {
        if (res.data.code == '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.sum.allCount
          this.tableLoading = false
        }
      })
    },
    // insp 资产
    getInspAssetsListData() {
      const params = {
        ...this.inspAssetSearchParams,
        pageSize: this.pageSize,
        assetStatusCode: this.dialogData.assetStatusCode || '',
        currentPage: this.currentPage,
        professionalCategoryCode: this.dialogData.professionalCode || ''
      }
      getInspAssetDetails(params).then((res) => {
        if (res.data.code == '200') {
          this.tableData = res.data.data.assetDetailsList
          this.total = res.data.data.sum
          this.tableLoading = false
        }
      })
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    handleDetailEvent(row) {
      this.$emit('handleDetailImage', {...row, objType: this.dialogData.objType})
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.all-table-componentList {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
.preview-image {
  z-index: 9999 !important;
  ::v-deep .el-image-viewer__canvas {
    color: #fff;
  }
}
::v-deep .mainDialog {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-tabs__nav-scroll {
    display: flex;
    .el-tabs__nav {
      margin: auto;
    }
  }
  .title-tabs {
    margin-bottom: 10px;
    .el-tabs__header {
      margin: 0;
      .el-tabs__active-bar {
        background: #ffe3a6;
      }
      .el-tabs__item.is-active {
        color: #ffe3a6;
      }
      .el-tabs__item {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;
      .statistics-top {
        height: 70px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        > div {
          width: max-content;
          // width: 10%;
          height: 100%;
          padding: 10px 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          p {
            text-align: center;
            font-size: 1rem;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .green-font {
            font-size: 1.25rem;
            color: #ffca64;
          }
        }
      }
      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background-color: transparent !important;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
    .el-table th.el-table__cell > .cell {
      width: max-content;
    }
    .el-table td.el-table__cell .cell div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
::v-deep .search-box {
  display: flex;
  flex-wrap: wrap;
  // margin-bottom: 12px;
  .riskInput {
    .el-input {
      width: 110px;
    }
    .el-date-editor{
      .el-range-input{
        margin-left: 11px;
      }
    }

    .el-select {
      .el-input__inner {
        width: 110px !important;
      }
    }
  }
  .taskInput {
    .el-input {
      width: 140px;
    }
  }
  .assetsInput {
    .el-input {
      width: 140px;
    }
  }
  .deviceInput {
    width: 150px;
    .el-input {
      width: 100%;
    }
    .el-cascader {
      line-height: 35px;
    }
  }
  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box{
  margin: 5px 0 15px ;
}

.search-box .el-input {
  width: 120px;
  height: 35px;
}
.transY {
  display: inline-block;
  transform: translateY(-2px);
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
