/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:17:28
 * @Last Modified by: mikey.zhaopengng
 * @Last Modified time: 2023-10-28 11:53:23
 */

import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
// 获取原型对象上的push函数
const originalPush = VueRouter.prototype.push
// 修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}
const routes = [
  {
    path: '/',
    component: () => import('@/views/Home.vue'),
    children: [
      // {
      //   path: '',
      //   name: 'Index',
      //   component: () => import('@/views/index.vue')
      // },
      // 运行监测
      {
        path: '/operationMonitoring',
        name: 'OperationMonitoring',
        component: () => import('@/views/normalMode/leftScreen/operationMonitoring/operationMonitoring.vue')
      },
      // 应急处置老页面
      {
        path: '/EmergencyDisposalOld',
        name: 'EmergencyDisposalOld',
        component: () => import('@/views/normalMode/leftScreen/EmergencyDisposal/EmergencyDisposal.vue')
      },
      // 应急处置-新页面
      // 运行监测 世纪坛
      {
        path: '/operationMonitoringSJT',
        name: 'operationMonitoringSJT',
        component: () => import('@/views/normalMode/leftScreen/operationMonitoring/operationMonitoringSJT.vue')
      },
      // 应急处置
      {
        path: '/EmergencyDisposal',
        name: 'EmergencyDisposal',
        component: () => import('@/views/normalMode/leftScreen/EmergencyDisposalNew/index.vue')
      },
      // 战时模式报警功能弹窗
      {
        path: '/alarmDialogIframe',
        name: 'alarmDialogIframe',
        component: () => import('@/views/normalMode/leftScreen/EmergencyDisposal/alarmDialogIframe.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 应急处置(处置)
      {
        path: '/WarnDisposal',
        name: 'WarnDisposal',
        component: () => import('@/views/normalMode/leftScreen/EmergencyDisposal/WarnDisposal.vue')
      },
      // 人员管理 右屏
      {
        path: '/peopleWorkOrder',
        name: 'peopleWorkOrder',
        component: () => import('@/views/normalMode/rightScreen/peopleWorkOrder.vue')
      },
      // 人员管理 右屏 世纪坛
      {
        path: '/peopleWorkOrderSJT',
        name: 'peopleWorkOrderSJT',
        component: () => import('@/views/normalMode/rightScreen/peopleWorkOrderSJT.vue')
      },
      // 人员管理 工单管理
      {
        path: '/workOrderList',
        name: 'workOrderList',
        component: () => import('@/views/normalMode/rightScreen/workOrderList.vue'),
        meta: {
          padding: 0
        }
      },
      // 人员管理 工单详情
      {
        path: '/workOrderDetail',
        name: 'workOrderDetail',
        component: () => import('@/views/normalMode/rightScreen/workOrderDetail.vue')
      },
      // 工单创建 iframe
      {
        path: '/workOrderIframe',
        name: 'workOrderIframe',
        component: () => import('@/views/normalMode/rightScreen/workOrderIframe.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 用户管理
      {
        path: '/userManagement',
        name: 'userManagement',
        component: () => import('@/views/sysManagement/userManagement.vue')
      },
      // 应急队伍信息
      {
        path: '/emergencyTeam',
        name: 'emergencyTeam',
        component: () => import('@/views/emergencyTeam/EmergencyTeam.vue')
      },
      // 设备报警监测
      {
        path: '/alarmMonitoring',
        name: 'alarmMonitoring',
        component: () => import('@/views/alarmMonitoring/AlarmMonitoring.vue')
      },
      // 角色管理
      {
        path: '/roleManagement',
        name: 'roleManagement',
        component: () => import('@/views/sysManagement/roleManagement.vue')
      },
      // 字典管理
      {
        path: '/dictManagement',
        name: 'dictManagement',
        component: () => import('@/views/sysManagement/dictManagement.vue')
      },
      // 警情配置
      {
        path: '/warnManagement',
        name: 'warnManagement',
        component: () => import('@/views/sysManagement/warnManagement.vue')
      },
      // 菜单管理
      {
        path: '/menuManagement',
        name: 'menuManagement',
        component: () => import('@/views/sysManagement/menuManagement.vue')
      },
      // 移动轨迹
      {
        path: '/movementTrajectory',
        name: 'movementTrajectory',
        component: () => import('@/views/spaceManage/movementTrajectory.vue')
      },
      // 中屏 一站式
      {
        path: '/businessIOMS',
        name: 'businessIOMS',
        component: () => import('@/views/centerScreen/businessManagement/businessIOMS.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 一站式 区域分析
      {
        path: '/businessIOMSalaysis',
        name: 'businessIOMSalaysis',
        component: () => import('@/views/centerScreen/businessManagement/businessIOMSalaysis.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 医废
      {
        path: '/businessIMWS',
        name: 'businessIMWS',
        component: () => import('@/views/centerScreen/businessManagement/businessIMWS.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 医废 区域分析
      {
        path: '/businessIMWSalaysis',
        name: 'businessIMWSalaysis',
        component: () => import('@/views/centerScreen/businessManagement/businessIMWSalaysis.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 巡检
      {
        path: '/businessIPAS',
        name: 'businessIPAS',
        component: () => import('@/views/centerScreen/businessManagement/businessIPAS.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 巡检 区域分析
      {
        path: '/businessIPASalaysis',
        name: 'businessIPASalaysis',
        component: () => import('@/views/centerScreen/businessManagement/businessIPASalaysis.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 中屏 能耗总览
      {
        path: '/energyConsumption',
        name: 'energyConsumption',
        component: () => import('@/views/centerScreen/businessManagement/energyConsumption.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // iframe 报警table
      {
        path: '/warnTable',
        name: 'warnTable',
        component: () => import('@/views/centerScreen/iframe/warnTable.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 安全态势总览
      {
        path: '/safetyOverview',
        name: 'SafetyOverview',
        component: () => import('@/views/centerScreen/safetyOverview/index.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 安全态势总览 楼层隐患清单
      {
        path: '/floorList',
        name: 'FloorList',
        component: () => import('@/views/centerScreen/safetyOverview/floorList.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 隐患详情
      {
        path: '/hiddenDangerDetails',
        name: 'HiddenDangerDetails',
        component: () => import('@/views/centerScreen/safetyOverview/hiddenDangerDetails.vue'),
        meta: {
          bgColor: '' // 透明背景
        }
      },
      // 危险点详情
      {
        path: '/riskPointDetails',
        name: 'RiskPointDetails',
        component: () => import('@/views/centerScreen/safetyOverview/riskPointDetails.vue'),
        meta: {
          bgColor: '' // 透明背景
        }
      },
      // 智能运行监控 组态视图
      {
        path: '/intelligentOperation',
        name: 'intelligentOperation',
        component: () => import('@/views/centerScreen/intelligentOperation/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 超时工单列表
      {
        path: '/overTimeScreen',
        name: 'overTimeScreen',
        component: () => import('@/views/centerScreen/businessManagement/overTimeScreen.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 资产
      {
        path: '/rearAssets',
        name: 'RearAssets',
        component: () => import('@/views/centerScreen/rearAssets/index.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 资产详情
      {
        path: '/assetsDetails',
        name: 'AssetsDetails',
        component: () => import('@/views/centerScreen/rearAssets/assetsDetails.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // ---------------------------------------------------------------
      // 空间管理
      {
        path: '/spaceManage',
        name: 'SpaceManage',
        component: () => import('@/views/spaceManage/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 医工管理
      {
        path: '/medicalManagement',
        name: 'medicalManagement',
        component: () => import('@/views/medicalManagement/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 医工管理 监测详情
      {
        path: '/monitorDetails',
        name: 'monitorDetails',
        component: () => import('@/views/medicalManagement/sysTypeComponent/monitorAssetsDetailsComponent.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // {
      //   path: '/roomInfoManagement',
      //   name: 'roomInfoManagement',
      //   component: () => import('@/views/spaceManage/roomInfoManagement.vue'),
      //   meta: {
      //     bgColor: 'transparent' // 透明背景
      //   }
      // },
      // {
      //   path: '/deviceInfoManagement',
      //   name: 'deviceInfoManagement',
      //   component: () => import('@/views/spaceManage/deviceInfoManagement.vue'),
      //   meta: {
      //     bgColor: 'transparent' // 透明背景
      //   }
      // },
      // 巡检详情
      {
        path: '/patrolInspectionDetails',
        name: 'PatrolInspectionDetails',
        component: () => import('@/views/spaceManage/patrolInspectionDetails.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 空调系统
      {
        path: '/airconditionSystem',
        name: 'airconditionSystem',
        component: () => import('@/views/spaceManage/airconditionSystem.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯系统(老的)
      {
        path: '/elevatorMonitor',
        name: 'elevatorMonitor',
        component: () => import('@/views/elevator/elevatorMonitor/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯系统（四军医口腔医院）
      {
        path: '/elevatorMonitorSJY',
        name: 'elevatorMonitorSJY',
        component: () => import('@/views/elevator/elevatorMonitorSJY/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯系统吉大医
      {
        path: '/elevatorMonitorJDY',
        name: 'elevatorMonitorJDY',
        component: () => import('@/views/elevator/elevatorMonitorJDY/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯系统
      {
        path: '/elevatorMonitorOld',
        name: 'elevatorMonitorOld',
        component: () => import('@/views/elevatorOld/elevatorMonitor/index.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯系统 对讲box页面
      {
        path: '/cameraTalkBox',
        name: 'cameraTalkBox',
        component: () => import('@/views/elevatorOld/cameraTalkBox.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 电梯系统 详情
      {
        path: '/elevatorDetail',
        name: 'elevatorDetail',
        component: () => import('@/views/elevatorOld/elevatorDetail.vue'),
        meta: {
          bgColor: 'transparent', // 透明背景
          padding: 0
        }
      },
      // 电梯事件管理
      {
        path: '/elevatorEmergencyDisposal',
        name: 'elevatorEmergencyDisposal',
        component: () => import('@/views/elevatorOld/elevatorEmergencyDisposal.vue'),
        meta: {
          padding: 0
        }
      },
      // 电梯事件管理(处置)
      {
        path: '/elevatorWarnDisposal',
        name: 'elevatorWarnDisposal',
        component: () => import('@/views/elevatorOld/elevatorWarnDisposal.vue'),
        meta: {
          padding: 0
        }
      },
      // 风险隐患视图
      {
        path: '/riskHiddenDanger',
        name: 'riskHiddenDanger',
        component: () => import('@/views/spaceManage/riskHiddenDanger.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 设备防盗
      {
        path: '/equipmentAntiTheft',
        name: 'EquipmentAntiTheft',
        component: () => import('@/views/spaceManage/equipmentAntiTheft.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 管线管理
      {
        path: '/pipelineManage',
        name: 'PipelineManage',
        component: () => import('@/views/spaceManage/pipelineManage.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // ---------------------------------------------------------------
      // 应急预案
      {
        path: '/emergencyPlan',
        name: 'emergencyPlan',
        component: () => import('@/views/wartimeMode/emergencyPlan/index.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 预案详情
      {
        path: '/planDetails',
        name: 'planDetails',
        component: () => import('@/views/wartimeMode/emergencyPlan/planDetails.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 法规文档
      {
        path: '/regulationsDoc',
        name: 'regulationsDoc',
        component: () => import('@/views/wartimeMode/emergencyPlan/regulationsDoc.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },
      // 法规文案
      {
        path: '/regulationsText',
        name: 'regulationsText',
        component: () => import('@/views/wartimeMode/emergencyPlan/regulationsText.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      },

      // 第四军医综合统计
      {
        path: '/comprehensiveStatistics',
        name: 'comprehensiveStatistics',
        component: () => import('@/views/surgeonGeneral/comprehensiveStatistics/index.vue'),
        meta: {
          bgColor: 'transparent' // 透明背景
        }
      }
    ]
  }
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/login.vue')
  // }
]

const router = new VueRouter({
  mode: 'history',
  base: __PATH.BASE_URL,
  routes
})

export default router
