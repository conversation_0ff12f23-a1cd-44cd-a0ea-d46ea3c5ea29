<template>
  <div class="floorSpaceComponent">
    <!-- <div class="top_tab2">
      <div class="tab-kj">空间分布</div>
    </div> -->
    <div class="space-content">
      <ModuleCard title="空间概览" :floorName="title ? title : ''" style="height: 32%">
        <div slot="content" class="top-content" style="height: 100%">
          <div class="top-fiex">
            <div class="top-fiex-div">
              <div class="fiex-tu">
                <div class="left"></div>
                <div class="right">
                  <div>房间总数</div>
                  <div class="right-bot point-cursor" @click="onWhole('', 'init')">
                    <span>{{ spaceData.totalCount }}</span>
                    <span>间</span>
                  </div>
                </div>
              </div>
              <div class="fiex-tu">
                <div class="left"></div>
                <div class="right">
                  <div>闲置房间</div>
                  <div class="right-bot point-cursor" @click="onWhole(0, 'init')">
                    <span>{{ spaceData.idleCount }}</span>
                    <span>间</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="top-fiex-div">
              <div class="fiex-tu">
                <div class="left-to left"></div>
                <div class="right">
                  <div>公区面积</div>
                  <div class="right-bot">
                    <span>{{ spaceData.publicArea }}</span>
                    <span>m²</span>
                  </div>
                </div>
              </div>
              <div class="fiex-tu">
                <div class="left left-to"></div>
                <div class="right">
                  <div>使用面积</div>
                  <div class="right-bot">
                    <span>{{ spaceData.useArea }}</span>
                    <span>m²</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="top-fiex-div">
              <div class="fiex-tu">
                <div class="left left-to"></div>
                <div class="right">
                  <div>建筑面积</div>
                  <div class="right-bot">
                    <span>{{ spaceData.totalArea }}</span>
                    <span>m²</span>
                  </div>
                </div>
              </div>
              <div></div>
            </div>
          </div>
        </div>
      </ModuleCard>
      <ModuleCard :title="ssmType < 5 ? '空间排名' : '部门显示色'" class="middle-content" style="min-height: 15%; max-height: 32%" :style="{ height: ssmType < 5 ? '32%' : 'auto' }">
        <div slot="title-right" class="middle-right">
          <div :class="{ 'is-activeTab': isType === 'bm' }" @click="onKg('bm')">部门</div>
          <div :class="{ 'is-activeTab': isType === 'yt' }" @click="onKg('yt')">用途</div>
          <div v-if="ssmType < 5" :class="{ 'is-activeTab': isType === 'jz' }" @click="onKg('jz')">建筑</div>
        </div>
        <div slot="content" style="height: 98%">
          <div v-if="ssmType < 5" class="middle-py top-title-box">
            <div class="">区域空间概述</div>
            <div class="middle-bot">
              <el-dropdown trigger="click" @command="handleCommand">
                <span class="el-dropdown-link middle-font"> {{ isCommand === 'fj' ? '房间数' : '面积' }}<i class="el-icon-arrow-down el-icon--right middle-icon"></i> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="fj" :class="{ isBjxl: isCommand === 'fj' }">房间数</el-dropdown-item>
                  <el-dropdown-item command="mg" :class="{ isBjxl: isCommand === 'mg' }">面积</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div v-if="ssmType < 5" class="middle-tj">
            <div id="EChart" style="height: 100%; overflow: hidden"></div>
          </div>
          <!-- 整层 -->
          <div v-else class="middle-zc">
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" :class="{ checkboxShow: isIndeterminate }" class="checkbox-title" @change="changeCheckAll">全选</el-checkbox>
            <el-checkbox-group v-model="checkList" class="middle-xz" @change="changeCheckBox">
              <div v-for="(item, index) in tableData" :key="index" class="fiex-all">
                <el-checkbox :key="item.id" :label="item.id" :value="item.id" class="checkbox"></el-checkbox>
                <div class="fiex-div-xz" :style="{ background: item.background }"></div>
                <div class="fiex-div-font">{{ item.name }}</div>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </ModuleCard>
      <ModuleCard title="部门排名分布" :hasExpand="true" class="bottom-content" @emit-expand="onWhole('')">
        <div slot="content" class="bottom-tab">
          <div v-if="spaceList && spaceList.length" class="space-breadcrumb">
            <ul>
              <li v-for="(v, i) in spaceList" :key="i" @click="goLastLevel(v, i)">{{ v.functName }} <i class="el-icon-arrow-right"></i></li>
            </ul>
            <!-- <el-breadcrumb separator-class="el-icon-arrow-right">
              <el-breadcrumb-item v-for="(item, index) in spaceList" :key="index">{{ item.functName }}</el-breadcrumb-item>
            </el-breadcrumb> -->
          </div>
          <el-table
            v-el-table-infinite-scroll="tableLoadEvent"
            v-scrollHideTooltip
            :data="tableDatas"
            style="width: 100%"
            :height="spaceList.length ? 'calc((100% - 30px)' : 'calc(100%)'"
            class="bottom-el-table"
            @row-click="goDeatilList"
          >
            <el-table-column :prop="isType === 'jz' ? 'ssmName' : isType === 'yt' && ssmType < 5 ? 'functName' : 'deptName'" :label="getTableLabel" min-width="129" show-overflow-tooltip>
            </el-table-column>
            <el-table-column :prop="isType === 'jz' ? 'modelRoomCount' : 'spaceNum'" label="房间数" min-width="129" show-overflow-tooltip> </el-table-column>
            <el-table-column :prop="isType === 'jz' ? 'build' : 'build'" label="分布楼宇" min-width="130" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
      </ModuleCard>
    </div>
    <template v-if="allTableComponentListShow">
      <spaceRoomStatistics
        :dialogVisible="allTableComponentListShow"
        :dialogTitle="roomData.title"
        :paramsData="paramsData"
        :modelCode="roomData.modelCode"
        :spaceState="spaceState"
        @dialogVisible="configCloseTableDialog"
      >
      </spaceRoomStatistics>
    </template>
  </div>
</template>

<script>
import spaceRoomStatistics from '../components/spaceRoomStatistics.vue'
import {
  getRoomCountAndArea,
  getRoomAreaList,
  getRoomCountList,
  getSpaceDeptList,
  getRoomCountAndAreaPageList,
  getRoomCountByPidModelAndLevel,
  getRoomCountByDeptIdList,
  getDeptSpaceInfoByFunctId,
  lookUpByModelCode,
  getRoomStatisByFunctId
} from '@/utils/spaceManage.js'
import * as echarts from 'echarts'
export default {
  name: 'floorSpaceComponent',
  components: {
    spaceRoomStatistics
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      toWPFdata: '',
      allTableComponentListShow: false,
      activeTab: '1',
      activeLeftType: '',
      // table组件参数
      tableCompenentData: {
        title: '空间清单',
        type: 'space',
        height: 'calc(100% - 50px)'
      },
      // 空间管理数据
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      },
      spaceList: [], // 面包屑查询list
      spaceState: '',
      tableLoading: false,
      tableData: [],
      tableColumn: [],
      // spaceTableColumnData: [
      //   [
      //     {
      //       prop: 'name',
      //       label: '科室名称',
      //       label2: '功能分类'
      //     },
      //     {
      //       prop: 'value',
      //       label: '房间数(间)',
      //       label2: '房间数(间)'
      //     }
      //   ],
      //   [
      //     {
      //       prop: 'name',
      //       label: '科室名称',
      //       label2: '功能分类'
      //     },
      //     {
      //       prop: 'value',
      //       label: '建筑面积(㎡)',
      //       label2: '建筑面积(㎡)'
      //     }
      //   ],
      //   [
      //     {
      //       prop: 'name',
      //       label: '科室名称',
      //       label2: '功能分类'
      //     },
      //     {
      //       prop: 'value',
      //       label: '使用面积(㎡)',
      //       label2: '使用面积(㎡)'
      //     }
      //   ]
      // ],
      multipleSelection: [], // 选中的数据
      rowSelectFlag: true, // 行选中标识开关
      tableDatas: [], // 部门统计表数据
      tableDatasNew: [],
      checkAll: false, // 全选
      checkList: [], // 选中数组
      isIndeterminate: true,
      isType: 'bm', // 部门 用途 建筑
      isCommand: 'fj', // 房间数 面积
      ssmType: '', // 3为外院 4为建筑 5为楼层 6为房间
      title: '',
      regex: /^(\d{8})\s(.*)/,
      paramsData: {}
    }
  },
  computed: {
    // 获取表格label
    getTableLabel() {
      if (this.ssmType >= 5) {
        return '部门名称'
      } else {
        if (this.isType === 'bm') {
          return '部门名称'
        } else if (this.isType === 'yt') {
          return '用途名称'
        } else if (this.isType === 'jz') {
          return '建筑名称'
        } else {
          return ''
        }
      }
    }
  },
  watch: {
    /**
     * roomData: 对应监听的房间数据
     * @property {string} localtion - 0100112.
     * @property {number} ssmType - 5. 3为外院 4为建筑 5为楼层 6为房间
     * @property {string} ssmCodes - #,1574997196057620481,1574997196330250241,1574997196833566721,1574997197995388929.
     * @property {string} modelCode - BJSJTYY0100112.
     * @property {string} title - 门诊急诊综合楼综合楼 > 9F.
     */
    roomData: {
      handler(val) {
        this.initSpaceEvent(val)
      },
      deep: true
    }
  },
  mounted() {
    this.initSpaceEvent(this.roomData)
    // console.log(this.roomData, 'spaceMounted');
    // if (__PATH.VUE_APP_HOSPITAL_NODE == 'bjsyzyyy') {
    //   this.spaceTableColumnData[0].push({
    //     prop: 'roomProperty',
    //     label: '房间属性',
    //     label2: '房间属性'
    //   })
    // }
    // 空间管理数据接口
    // this.getRoomCountAndAreaList()
    // 初始化部门统计表
    // this.initFloorSpace()
  },
  methods: {
    initSpaceEvent(data) {
      let { ssmType, title } = data
      this.ssmType = ssmType
      this.title = title
      this.isType = 'bm'
      this.activeLeftType = 'dept'
      this.getRoomCountAndAreaList()
      if (ssmType < 5) {
        this.onKg('bm')
        // echarts初始化
        // this.getRenderer()
        // this.initFloorSpace()
      } else {
        this.changeType()
        // 部门用途染色
        if (ssmType == 6) {
          this.getSpaceBaseInfo()
        } else {
          this.getDeptSpaceInfoByFunctId()
        }
      }
    },
    // 点击房间获取详情
    getSpaceBaseInfo() {
      let params = {
        modelCode: this.roomData.modelCode
      }
      lookUpByModelCode(params).then((res) => {
        const data = res.data
        if (data.code === 200) {
          const { dmName, build, area, useArea, spaceState } = data.data
          //   计算公共区域面积 = 建筑面积 - 使用面积
          let publicArea = area - useArea
          // 保留两位小数
          const num = publicArea?.toString()
          if (num.indexOf('.') !== -1) {
            publicArea = num.substring(0, num.indexOf('.') + 3)
          }
          // 院区空间概览详情
          // this.spaceData = {
          //   idleCount: spaceState == '使用中' ? 1 : 0,
          //   publicArea: publicArea,
          //   totalArea: area,
          //   totalCount: 1, // 房间总数
          //   useArea: useArea
          // }
          this.tableDatas = [{ deptName: dmName, spaceNum: 1, build }]
        }
      })
    },
    // 获取空间信息房间数据
    getRoomCountAndAreaList() {
      const params = {
        modelCode: this.roomData.modelCode || '',
        haveModel: 0
      }
      getRoomCountAndArea(params).then((res) => {
        const data = res.data
        if (data.code === 200) {
          Object.assign(this.spaceData, data.data)
          // 计算公共区域面积 = 建筑面积 - 使用面积
          this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea
          // 保留两位小数
          const num = this.spaceData.publicArea?.toString()
          if (num.indexOf('.') !== -1) {
            this.spaceData.publicArea = num.substring(0, num.indexOf('.') + 3)
          }
        }
      })
    },
    // 获取用途统计表数据
    getRoomCountAndAreaPageList(command) {
      let datas = {
        current: 1,
        size: 999,
        modelCode: this.roomData.modelCode,
        queryType: 'function', // 用途 function 建筑 space
        ascColumn: '',
        descColumn: '',
        haveModel: '',
        ids: ''
      }
      getRoomCountAndAreaPageList(datas).then((res) => {
        const { data } = res
        if (data.code === 200) {
          let list = data.data.records
          let arr = []
          list.forEach((item) => {
            arr.push({
              name: item.dataName || '未分配',
              value: command === 'fj' ? item.roomAllCount : item.totalArea,
              id: item.dataNameId
            })
          })
          // 初始化空间排名统计表
          this.getRenderer(arr)
        }
      })
    },
    // 获取用途下面得部门
    getDeptSpaceInfoByFunctId(functId) {
      let datas = {
        functId: functId || '', // 用途id
        modelCode: this.roomData.modelCode
      }
      if (this.ssmType == 5) {
        Object.assign(datas, {
          floorCode: this.roomData.modelCode
          // modelCodes: 'BJSJTYY0100111150,BJSJTYY0100111055,BJSJTYY0100101149,BJSJTYY0101701003',
        })
      }
      getDeptSpaceInfoByFunctId(datas).then((res) => {
        const { data } = res
        if (data.code === 200) {
          let list = data.data
          list.forEach((item) => {
            item.deptName = item.deptName || '未命名'
            const match = item.deptName.match(this.regex)
            item.deptName = match ? match[2].trimLeft() : item.deptName
            // console.log(match, '12131')
            // 使用正则提取deptName中得中文
            // if (item.deptName != null) {
            //   item.deptName = item.deptName?.match(/[\u4e00-\u9fa5]+/g)?.join('')
            // }
          })
          this.tableDatas = list.sort((a, b) => b.spaceNum - a.spaceNum)
        }
      })
    },
    // 获取部门统计表数据
    getRoomCountByDeptIdList(command, deptId) {
      let datas = {
        deptId: deptId || '',
        modelCode: this.roomData.modelCode
      }
      if (this.ssmType == 5) {
        Object.assign(datas, {
          modelCode: this.roomData.modelCode
        })
      }
      getRoomCountByDeptIdList(datas).then((res) => {
        const { data } = res
        if (data.code === 200) {
          let list = data.data
          let arr = []
          list.forEach((item) => {
            arr.push({
              name: item.deptName || '未命名',
              value: command === 'fj' ? item.spaceNum : item.spaceArea,
              id: item.deptId
            })
          })
          list.forEach((item) => {
            item.deptName = item.deptName || '未命名'
            const match = item.deptName.match(this.regex)
            item.deptName = match ? match[2].trimLeft() : item.deptName
            // item.deptName = item.deptName?.match(/[\u4e00-\u9fa5]+/g)?.join('')
          })
          this.tableDatas = list.sort((a, b) => b.spaceNum - a.spaceNum)
          // 初始化空间排名统计表
          if (deptId) return
          this.getRenderer(arr)
        }
      })
    },
    // 获取建筑
    getRoomCountByPidModelAndLevel(modelCode, type) {
      let datas = {
        level: type === 4 ? type : 3, // 3是楼宇，4是楼层，
        pidModelCode: type === 4 ? modelCode : this.roomData.modelCode,
        haveModel: 0
      }
      getRoomCountByPidModelAndLevel(datas).then((res) => {
        const { data } = res
        if (data.code === 200) {
          let list = data.data
          let arr = []
          list.forEach((item) => {
            arr.push({
              spaceId: item.id,
              name: item.ssmName,
              value: this.isCommand === 'fj' ? item.modelRoomCount : item.area,
              id: item.modelCode
            })
          })
          this.tableDatas = list.sort((a, b) => b.modelRoomCount - a.modelRoomCount)
          if (type === 4) return
          // 初始化空间排名统计表
          this.getRenderer(arr)
        }
      })
    },
    // 初始化统计表
    getRenderer (data) {
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('EChart'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = match ? match[2].trimLeft() : item.name
        // 如果name大于8个字符就...
        const displayName = name.length > 8 ? name.slice(0, 8) + '...' : name
        return {
          value: displayName,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '0%',
          left: '8%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return value + (this.isCommand == 'fj' ? '间' : '㎡')
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {
              normal: {
                barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#0A84FFFF'
                }
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                isSelected = true
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
          // ....  业务逻辑
          // 取消选中 则恢复过滤条件
          let id = !isSelected ? yData.id : ''
          if (this.isType === 'bm') {
            this.paramsData = {
              deptId: id,
              spaceState: ''
            }
            this.getRoomCountByDeptIdList(this.isCommand, id)
          } else if (this.isType === 'yt') {
            this.paramsData = {
              functionDictId: id,
              spaceState: ''
            }
            let data = {
              functId: id
            }
            this.goDeatilList(data, '2')
          } else if (this.isType === 'jz') {
            this.paramsData = {
              modelCode: id,
              spaceState: ''
            }
            this.getRoomCountByPidModelAndLevel(id, !isSelected ? 4 : 3)
            if (this.ssmType < 4) {
              try {
                window.chrome.webview.hostObjects.sync.bridge.WebSwitchingPerspective(!isSelected ? yData.spaceId : '')
              } catch (error) {}
            }
          }
        }
      })
    },
    // 点击空间排名相关事件  部门 用途 建筑
    onKg(type) {
      this.paramsData = {
        spaceState: ''
      }
      this.isType = type
      this.spaceList = []
      if (this.ssmType >= 5) {
        this.changeType(type)
      } else {
        if (type === 'bm') {
          this.getRoomCountByDeptIdList(this.isCommand)
        } else if (type === 'yt') {
          this.getRoomStatisByFunctId(this.isCommand)
          // this.getRoomCountAndAreaPageList(this.isCommand)
          // this.getDeptSpaceInfoByFunctId()
        } else if (type === 'jz') {
          this.getRoomCountByPidModelAndLevel()
        }
      }
    },
    // 获取面包屑导航菜单
    getBreadcrumbList(val) {
      if (this.tableDatas && this.tableDatas.length && this.tableDatas.length > 0) {
        this.tableDatas.forEach((ele) => {
          if (ele.functId == val) {
            this.spaceList.push(ele)
          }
        })
      }
    },
    getBreadcrumbList1(val) {
      this.spaceList = []
      if (this.tableDatasNew && this.tableDatasNew.length && this.tableDatasNew.length > 0) {
        this.tableDatasNew.forEach((ele) => {
          if (ele.functId == val) {
            this.spaceList.push(ele)
          }
        })
      }
    },
    // 获取上一级level
    goLastLevel(el, index) {
      if (this.isType == 'yt') {
        this.paramsData = {
          functionDictId: el.functId,
          spaceState: ''
        }
        let datas = {
          modelCode: this.roomData.modelCode,
          parentFunctId: el.functId,
          column: this.isCommand == 'fj' ? 'spaceNum' : 'area'
        }
        getRoomStatisByFunctId(datas).then((res) => {
          const { data } = res
          if (data.code === 200) {
            let list = data.data
            // let arr = []
            // list.forEach((item) => {
            //   arr.push({
            //     name: item.functName || '未命名',
            //     value: this.isCommand === 'fj' ? 20 : 30,
            //     id: item.functId
            //   })
            // })
            this.spaceList = this.spaceList.slice(0, index + 1)
            this.getBreadcrumbList(el.functId)
            this.tableDatas = list.sort((a, b) => b.spaceNum - a.spaceNum)
            // this.getRenderer(arr)
          }
        })
      }
    },
    // 根据table查list
    goDeatilList(row, type) {
      // 进到三级或者到楼层及以后不能点击
      if (this.spaceList.length >= 2 || this.ssmType >= 5) return
      if (this.isType == 'yt') {
        this.paramsData = {
          functionDictId: row.functId,
          spaceState: ''
        }
      }
      if (this.isType == 'yt') {
        let datas = {
          modelCode: this.roomData.modelCode,
          parentFunctId: row.functId,
          column: this.isCommand == 'fj' ? 'spaceNum' : 'area'
        }
        getRoomStatisByFunctId(datas).then((res) => {
          const { data } = res
          if (data.code === 200) {
            let list = data.data
            if (type == '2') {
              this.getBreadcrumbList1(row.functId)
            } else {
              this.getBreadcrumbList(row.functId)
            }
            this.tableDatas = list.sort((a, b) => b.spaceNum - a.spaceNum)
          }
        })
      }
    },
    // 获取用途图表和列表
    getRoomStatisByFunctId(command, parentId) {
      let datas = {
        modelCode: this.roomData.modelCode,
        parentFunctId: parentId,
        column: command == 'fj' ? 'spaceNum' : 'area'
      }
      if (this.ssmType == 5) {
        Object.assign(datas, {
          modelCode: this.roomData.modelCode
        })
      }
      getRoomStatisByFunctId(datas).then((res) => {
        const { data } = res
        if (data.code === 200) {
          let list = data.data
          let arr = []
          list.forEach((item) => {
            arr.push({
              name: item.functName || '未命名',
              value: command === 'fj' ? item.spaceNum : item.spaceArea,
              id: item.functId
            })
          })
          this.tableDatasNew = list
          this.tableDatas = list.sort((a, b) => b.spaceNum - a.spaceNum)
          this.getRenderer(arr)
        }
      })
    },

    // 点击下拉菜单
    handleCommand(command) {
      this.isCommand = command
      this.spaceList = []
      if (this.isType === 'bm') {
        this.getRoomCountByDeptIdList(command)
      } else if (this.isType === 'yt') {
        this.getRoomStatisByFunctId(command)
      } else if (this.isType === 'jz') {
        this.getRoomCountByPidModelAndLevel()
      }
    },
    // 点击打开弹窗
    onWhole(spaceState, type) {
      this.spaceState = spaceState
      if (type == 'init') {
        this.paramsData = {}
      }
      this.allTableComponentListShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    // 懒加载
    tableLoadEvent() {
      console.log('懒加载')
    },
    // initFloorSpace() {
    //   this.rowSelectFlag = true
    //   // 初始化表头赋值
    //   this.tableColumn = this.spaceTableColumnData[this.activeTab - 1]
    //   // this.getSpaceMessageList()
    //   //   this.getRoomCountAndArea()
    // },
    // 获取空间信息房间数据
    // getRoomCountAndArea() {
    //   const params = {
    //     modelCode: this.roomData.modelCode,
    //     haveModel: 0
    //   }
    //   getRoomCountAndArea(params).then((res) => {
    //     const data = res.data
    //     if (data.code === 200) {
    //       Object.assign(this.spaceData, data.data)
    //     }
    //   })
    // },
    // spaceTableChange(params) {
    //   this.allTableComponentListShow = true
    //   Object.assign(this.tableCompenentData, {
    //     modelCode: this.roomData.modelCode,
    //     deptId: '',
    //     functionDictId: '',
    //     ...params
    //   })
    // },
    // changeTab(tab) {
    //   // this.multipleSelection = []
    //   this.activeTab = tab
    //   this.tableColumn = this.spaceTableColumnData[tab - 1]
    //   this.getSpaceMessageList()
    // },
    changeType(type = 'bm') {
      this.activeLeftType = type === 'bm' ? 'dept' : 'function'
      this.getSpaceMessageList()
    },
    // 获取空间列表数据
    getSpaceMessageList() {
      const params = {
        modelCode: this.roomData.modelCode
        // queryType: this.activeLeftType
      }
      this.tableLoading = true
      if (this.activeTab !== '1') {
        getRoomAreaList(params).then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code === 200) {
            const valueData = data.data.map((e, index) => {
              e.dataName = e.dataName || '未分配'
              const match = e.dataName.match(this.regex)
              return {
                value: e.roomAllArea ?? 0,
                name: match ? match[2].trimLeft() : e.dataName,
                // name: e.dataName.match(/[\u4e00-\u9fa5]+/g)?.join('') ?? '未分配',
                id: index,
                background: e.dataColorBy255 ? `rgba(${e.dataColorBy255})` : 'rgba(139, 221, 245, 1)',
                ...e
              }
            })
            // const idx = valueData.findIndex((item) => item.name === '未分配')
            // valueData.push(valueData[idx])
            // valueData.splice(idx, 1)
            // valueData.sort((a, b) => {
            //   return b.value - a.value
            // })
            this.tableData = valueData
            this.getAllCheckColorData()
            // this.setSelectRowData()
          }
        })
      } else {
        if (this.activeLeftType === 'function') {
          params.queryType = 'function'
          getRoomCountList(params).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === 200) {
              const valueData = data.data.map((e, index) => {
                e.dataName = e.dataName || '未分配'
                const match = e.dataName.match(this.regex)
                return {
                  // value: e.roomAllCount ?? 0,
                  name: match ? match[2].trimLeft() : e.dataName,
                  // name: e.dataName.match(/[\u4e00-\u9fa5]+/g)?.join('') ?? '未分配',
                  // roomProperty: e.roomProperty ?? '',
                  id: index,
                  background: e.dataColorBy255 ? `rgba(${e.dataColorBy255})` : 'rgba(139, 221, 245, 1)',
                  roomCodes: e.roomCodes ?? ''
                  // ...e
                }
              })
              // const idx = valueData.findIndex((item) => item.name === '未分配')
              // valueData.push(valueData[idx])
              // valueData.splice(idx, 1)
              // valueData.sort((a, b) => {
              //   return b.value - a.value
              // })
              this.tableData = valueData
              this.getAllCheckColorData()
              // this.setSelectRowData()
            }
          })
        } else {
          getSpaceDeptList(params).then((res) => {
            const data = res.data
            if (data.code === 200) {
              const valueData = data.data?.map((e, index) => {
                e.dataName = e.deptName || '未分配'
                const match = e.dataName.match(this.regex)
                return {
                  // ...e,
                  name: match ? match[2].trimLeft() : e.dataName,
                  id: index,
                  background: e.functionColourBy255 ? `rgba(${e.functionColourBy255})` : 'rgba(139, 221, 245, 1)',
                  roomCodes: e.roomCodes ?? ''
                }
              })
              // 如果valueData的某一项name等于基建总务部，就把这条数据挪到数组的最后一位（现场特殊需求，后期可删掉）
              const index = valueData.findIndex((item) => item.name?.trim() == '基建总务部')
              if (index !== -1) {
                valueData.push(valueData[index])
                valueData.splice(index, 1)
              }
              this.tableData = valueData
              this.getAllCheckColorData()
            }
          })
        }
      }
    },
    // 初始化获取所有选中的数据
    getAllCheckColorData() {
      if (this.tableData) {
        const allIDs = Array.from(this.tableData, ({ id }) => id)
        this.checkList = allIDs
        this.isIndeterminate = true
        this.setCheckDataToWpf()
      }
    },
    // 传输数据给wpf
    setCheckDataToWpf() {
      console.log(this.checkList, 'this.checkList')
      const checkedData = []
      this.checkList.map((item) => {
        let data = this.tableData.find((obj) => obj.id === item)
        if (data.roomCodes) {
          checkedData.push(data.roomCodes)
        }
      })
      const JSONdata = JSON.stringify({
        type: this.activeLeftType,
        selectIds: checkedData.toString(),
        modelCode: this.roomData.modelCode
      })
      console.log(JSONdata, 'JSONdata')
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetRoomCodeType(JSONdata)
      } catch (error) {}
    },
    changeCheckAll(val) {
      this.checkList = val ? Array.from(this.tableData, ({ id }) => id) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    changeCheckBox(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.tableData.length
      this.isIndeterminate = checkedCount > 0 && checkedCount >= this.tableData.length
      this.setCheckDataToWpf()
    }
    // 双击 以及操作点击事件
    // selectConfigRowData(row) {
    //   this.spaceTableChange({
    //     deptId: this.activeLeftType === 'dept' ? row.id : '',
    //     functionDictId: this.activeLeftType === 'function' ? row.id : ''
    //   })
    // },
    // setSelectRowData() {
    //   // this.multipleSelection = Array.from(this.tableData, ({ roomCodes }) => roomCodes) ?? []
    //   // const tempArr = this.multipleSelection
    //   // setTimeout(() => {
    //   this.rowSelectFlag = true
    //   this.$nextTick(() => {
    //     this.tableData.forEach((item, index) => {
    //       if (index === this.tableData.length - 1) {
    //         this.rowSelectFlag = false
    //       }
    //       // this.$refs.multipleTable.toggleRowSelection(item, true)
    //       // checkedData为已选数据
    //       // this.$nextTick(() => {
    //       //   this.tableData.find((obj) => {
    //       //     // userData 表单数据
    //       //     if (item === obj.roomCodes) {
    //       //       if (index === this.multipleSelection.length - 1) {
    //       //         this.rowSelectFlag = false
    //       //       }
    //       //       this.$refs.multipleTable.toggleRowSelection(obj, true)
    //       //     }
    //       //   })
    //       // })
    //     })
    //   })
    //   // }, 0)
    // },
    // handleSelectionChange(val) {
    //   if (this.rowSelectFlag) return
    //   this.multipleSelection = val
    //   let checkedDepts = []
    //   // if (this.activeLeftType === 'function') {
    //   checkedDepts = Array.from(this.multipleSelection, ({ roomCodes }) => roomCodes)
    //   // }
    //   const JSONdata = JSON.stringify({
    //     type: this.activeLeftType,
    //     selectIds: checkedDepts.toString(),
    //     modelCode: this.roomData.modelCode
    //   })
    //   this.$emit('roomEvent', {
    //     type: 'space',
    //     data: JSONdata
    //   })
    //   try {
    //     window.chrome.webview.hostObjects.sync.bridge.GetRoomCodeType(JSONdata)
    //   } catch (error) {}
    // }
  }
}
</script>

<style lang="scss" scoped>
.floorSpaceComponent {
  width: 100%;
  height: 100%;
  // padding-top: 5px;
  .top_tab2 {
    display: flex;
    align-items: center;
    padding: 12px 8px;
    background: rgba(133, 145, 206, 0.15);

    .tab-kj {
      width: 80px;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      text-align: center;
      color: #8bddf5;
      background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat;
    }
    .tab-kj:hover {
      cursor: pointer;
    }
  }
  .top-title-box {
    height: 32px;
    box-sizing: border-box;
    line-height: 18px;
    font-size: 15px;
    color: #ffffff;
    padding: 7px 8px;
    background: rgba(133, 145, 206, 0.3);
  }
  .top-title-big-box {
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    background: url('@/assets/images/qhdsys/bg-bt.png') no-repeat;
    background-size: 100% 100%;
  }
  .middle-pm {
    width: 96px;
    height: 30px;
    line-height: 30px;
    margin-left: 38px;
    font-size: 16px;
    font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0px 0px 9px #158eff;
  }
  .space-content {
    height: calc(100% - 5px);
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    .top-content {
      // height: calc(32%);
      background: rgba(133, 145, 206, 0.15);
      display: flex;
      flex-direction: column;
      .top-fiex {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .top-fiex-div {
          display: flex;
          // padding: 10px 8px 2px;
          .fiex-tu {
            width: 50%;
            display: flex;
            align-items: center;

            .left {
              width: 50.94px;
              height: 56.15px;
              background: url('@/assets/images/qhdsys/fj-tb.png') no-repeat;
            }

            .left-to {
              background: url('@/assets/images/qhdsys/mj-tb.png') no-repeat;
            }

            .right {
              display: flex;
              flex-direction: column;
              width: 124px;
              // height: 68px;

              .right-bot {
                width: 100%;
                // height: 100%;
                margin-top: 6px;
                display: flex;
                justify-content: center;
                background: url('@/assets/images/qhdsys/bg-tu.png') no-repeat;
              }

              .right-bot span:nth-child(1) {
                margin-top: 5px;
                //   width: 37px;
                height: 23px;
                font-size: 17px;
                font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
                font-weight: bold;
                color: #ffca64;
                line-height: 23px;
                // -webkit-background-clip: text;
              }

              .right-bot span:nth-child(2) {
                margin-top: 10px;
                margin-left: 3px;
                width: 14px;
                height: 16px;
                font-size: 13px;
                font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
                font-weight: 300;
                color: rgba(255, 255, 255, 0.5);
                line-height: 16px;
              }
            }

            .right div:nth-child(1) {
              font-size: 14px;
              color: #b0e3fa;
              margin-left: 18px;
            }
          }

          .fiex-tu:nth-child(1) {
            // margin-right: 32px;
          }
        }
      }
      .point-cursor {
        cursor: pointer;
      }
    }
    .middle-content {
      height: calc(32%);
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      .middle-kg {
        flex-shrink: 0;
        height: 44px;
        // margin-top: 24px;
        justify-content: space-between;
      }
      .middle-right {
        display: flex;
        align-items: center;
      }
      .is-activeTab {
        background: url('@/assets/images/qhdsys/bg-kj.png') no-repeat !important;
      }
      .middle-right div {
        cursor: pointer;
        width: 52px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #8bddf5;
      }
      .middle-right div:nth-child(2) {
        margin: 0 6px;
      }
      .middle-py {
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        padding: 7px 8px;
        .middle-bot {
          .middle-font {
            width: 42px;
            height: 16px;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
            font-weight: 300;
            color: #8bddf5;
            line-height: 16px;
          }
          .middle-icon {
            width: 10px;
            height: 10px;
            font-size: 12px;
          }
        }
        .middle-bot:hover {
          cursor: pointer;
        }
      }
      .middle-tj {
        height: calc(100% - 32px);
        overflow-y: auto;
      }
    }
    .bottom-content {
      height: calc(33%);
      overflow-y: auto;
      .middle-bm {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        .middle-sx {
          margin-right: 5px;
          display: flex;
        }
        .middle-sx div:nth-child(1) {
          width: 24px;
          height: 24px;
          background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat;
        }
        .middle-sx div:hover {
          cursor: pointer;
        }
      }
      .bottom-tab {
        .space-breadcrumb {
          padding: 5px 5px 8px 5px;
          font-size: 14px;
          cursor: default;
          ul li {
            display: inline-block;
            color: rgba(255, 255, 255, 0.8);
          }
          li:last-child {
            color: #ffffff;
          }
        }
        // height: calc(100% - 16px - 44px);
        height: calc(100%);
        ::v-deep .bottom-el-table {
          border: 0 !important;

          .el-table__header-wrapper {
            .el-table__header {
              .has-gutter {
                tr {
                  background: transparent;
                }

                tr th {
                  padding: 0;
                  height: 32px;
                  background: rgba(133, 145, 206, 0.15) !important;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.5);

                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
                    font-weight: bold;
                    color: #8bddf5;
                  }
                }
              }
            }
          }
        }
        ::v-deep .el-table__body-wrapper {
          background: transparent;
          overflow: hidden;
          overflow-y: auto;

          .el-table__body {
            background: transparent;

            tbody {
              background: transparent;

              .el-table__row {
                // background-color: rgba(255, 255, 255, 0.2);
                background: transparent;
                border: 0;

                td {
                  border: 0;
                  padding: 0;
                  height: 30px;

                  div {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                    font-weight: 400;
                    color: #ffffff;
                  }
                }
              }

              .el-table__row:nth-child(2n - 1) {
                background: rgba(168, 172, 171, 0.08);
              }

              .el-table__row:hover {
                border: 0;
                opacity: 1;
                cursor: pointer;

                td div {
                  color: rgba(255, 202, 100, 1);
                }
              }
            }
          }
        }
      }
    }
  }

  ::v-deep .middle-zc {
    // flex: 1;
    padding: 10px 0 0px 10px;
    height: calc(100%);
    // height: calc(100% - 54px);
    .middle-xz {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 16px);
      overflow-y: auto;
      .fiex-all {
        display: flex;
        align-items: center;
        margin-top: 8px;

        .fiex-div-xz {
          width: 197px;
          height: 8px;
          background: #8bddf5;
          margin: 0 8px;
          flex-shrink: 0;
        }

        .fiex-div-font {
          height: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .checkbox {
          width: 14px;
          height: 14px;
        }
        .el-checkbox__label {
          display: none;
        }
      }
    }
    .el-checkbox__input {
      .el-checkbox__inner {
        box-shadow: 0px 0px 3px 0px #78fff8;
        opacity: 1;
        border: 1px solid #52fffc;
        background: transparent;
      }
      .el-checkbox__inner::before {
        display: none;
      }
    }
    .checkboxShow {
      .el-checkbox__input {
        .el-checkbox__inner {
          background: url('@/assets/images/qhdsys/bg-checkall.png') no-repeat !important;
          border: 0;
        }
      }
    }
    .checkbox-title {
      .el-checkbox__label {
        color: #ffffff;
      }
    }
  }
}
</style>
