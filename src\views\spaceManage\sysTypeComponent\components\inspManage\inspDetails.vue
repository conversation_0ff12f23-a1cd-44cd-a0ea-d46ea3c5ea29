<template>
  <div class="inspDetails">
    <ModuleCard
      :showTitle="false"
      class="module-container"
      style="height: 25%; background: rgba(133, 145, 206, 0.15)"
    >
      <div slot="content" class="module-content info-list" style="height: 100%">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p v-if="item.key == 'distributionTeamName'" class="item-label">
            {{ menuName + item.label }}：
          </p>
          <p v-else class="item-label">{{ item.label }}：</p>

          <p v-if="item.key == 'taskStatus'" class="item-value">
            {{ infoData[item.key] == 1 ? "未完成" : "已完成" || "---" }}
          </p>
          <p v-else-if="item.key == 'cycleType'" class="item-value">
            {{ typeOptions[infoData[item.key]] || "---" }}
          </p>
          <p v-else class="item-value">{{ infoData[item.key] || "---" }}</p>
        </div>
        <span class="view-more" @click="viewMore">更多信息</span>
      </div>
    </ModuleCard>
    <ModuleCard :showTitle="false" class="module-container" style="height: 75%">
      <div slot="content" class="module-content" style="height: 100%">
        <el-table
          v-loading="pointTableLoading"
          v-el-table-infinite-scroll="pointTableLoadEvent"
          class="table-center-transfer"
          :data="pointTableData"
          height="100%"
          :cell-style="{
            padding: ' 8px',
            backgroundColor: 'transparent',
            border: 'none',
            padding: '3px',
          }"
          :header-cell-style="{
            background: 'rgba(133,145,206,0.08)!important',
            color: '#8BDDF5FF',
            padding: '4px 8px',
            fontWeight: 'bold',
          }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="toView"
        >
          <el-table-column
            fixed
            prop="taskPointName"
            show-overflow-tooltip
            :label="menuName + '点名称'"
          ></el-table-column>
          <el-table-column
            fixed
            prop="taskStatus"
            width="95"
            show-overflow-tooltip
            :label="menuName + '结果'"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.state == '3' || scope.row.state == '4'"
                class="table-icon"
              >
                <img src="@/assets/images/icon-2.png" />
                <span>{{
                  scope.row.state == "3" ? "不合格" : "异常报修"
                }}</span>
              </div>
              <div v-else-if="scope.row.state == '2'" class="table-icon">
                <img src="@/assets/images/icon-5.png" />
                <span>合格</span>
              </div>
              <div v-else>
                <span>未{{ menuName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            fixed
            prop="excuteTime"
            show-overflow-tooltip
            :label="menuName + '时间'"
          ></el-table-column>
          <!-- <el-table-column fixed prop="implementPersonName" show-overflow-tooltip label="执行人员"></el-table-column> -->
        </el-table>
      </div>
    </ModuleCard>
    <inspectionDetail
      v-if="isInspectionDetail"
      ref="inspectionDetail"
      :systemType="dialogData.menu === 'icis' ? 'xjrw' : 'byrw'"
      taskType="insp"
      :dataInfo="infoData"
      @closeDialog="isInspectionDetail = false"
    ></inspectionDetail>
  </div>
</template>

<script>
import { GetInspTaskPointReleaseList } from '@/utils/spaceManage'
export default {
  name: 'inspDetails',
  components: {
    inspectionDetail: () => import('../inspectionDetail.vue')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isInspectionDetail: false,
      menuName: '',
      infoData: {},
      infoList: [
        { label: '任务名称', key: 'taskName' },
        { label: '周期类型', key: 'cycleType' },
        { label: '部门', key: 'distributionTeamName' },
        { label: '完成状态', key: 'taskStatus' },
        { label: '应巡日期', key: 'taskEndTime' }
      ],
      typeOptions: {
        8: '单次',
        6: '每日',
        0: '每周',
        2: '每月',
        3: '季度',
        5: '全年',
        7: '自定义'
      },
      pointTableLoading: false,
      pointTableData: [],
      pageParams: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      }
    }
  },
  computed: {},
  watch: {
    dialogData: {
      handler(newVal) {
        this.pageParams.pageNo === 1
        this.isInspectionDetail = false
        this.menuName = newVal.menu == 'icis' ? '巡检' : '保养'
        this.getTaskPointTableData()
      },
      deep: true
    }
  },
  created() {
    this.menuName = this.dialogData.menu == 'icis' ? '巡检' : '保养'
    this.getTaskPointTableData()
  },
  methods: {
    viewMore() {
      this.isInspectionDetail = true
      setTimeout(() => {
        if (this.$refs.inspectionDetail) {
          this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
          this.$refs.inspectionDetail.GetTaskPointReleaseList(
            this.infoData.id,
            'insp'
          )
        }
      }, 100)
    },
    // 根据任务id获取巡检点清单
    getTaskPointTableData() {
      this.pointTableLoading = true
      const params = {
        taskId: this.dialogData.deviceId,
        pageNo: this.pageParams.pageNo,
        pageSize: this.pageParams.pageSize
      }
      GetInspTaskPointReleaseList(params).then((res) => {
        this.pointTableLoading = false
        if (res.data.code === '200') {
          this.pageParams.total = parseInt(res.data.data.sum)
          if (this.pageParams.pageNo === 1) this.pointTableData = []
          this.infoData = res.data.data.taskMap
          this.pointTableData = this.pointTableData.concat(res.data.data.list)
          // 设备点时向wpf发送消息
          const particulars = res.data.data.list[0].particulars ? JSON.parse(res.data.data.list[0].particulars) : ''
          if (particulars) {
            window.chrome.webview.hostObjects.sync.bridge.PatrolRealView(JSON.stringify({
              type: 'task',
              id: this.dialogData.deviceId,
              assetsId: particulars.assetsId,
              assetSn: particulars.assetSn,
              assetCode: particulars.assetCode
            }))
          }
          // 视频巡逻时需要自动跳转至详情
          if (this.dialogData.autoJump) {
            const noFinashList = this.pointTableData.filter(i => i.carryOutFlag != 1)
            const targetRow = noFinashList[0] || this.pointTableData[0]
            this.toView(targetRow)
          }
        }
      })
    },
    toView(row) {
      if (this.dialogData.taskType === 'video') {
        this.$emit('change', 'videoTask', {
          ...row,
          currentIndex: this.pointTableData.findIndex((item) => item.id == row.id),
          infoData: this.infoData,
          pointIds: this.pointTableData.map((item) => item.id)
        })
      } else {
        this.$emit('change', 'viewPoint', {
          ...row,
          currentIndex: this.pointTableData.findIndex((item) => item.id == row.id),
          pointIds: this.pointTableData.map((item) => item.id)
        })
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.PatrolRealView(
          JSON.stringify({
            type: 'point',
            id: row.id,
            assetsId: row.particulars ? JSON.parse(row.particulars).assetsId : '',
            assetSn: row.particulars ? JSON.parse(row.particulars).assetSn : '',
            assetCode: row.particulars ? JSON.parse(row.particulars).assetCode : ''
          })
        )
      } catch (error) {}
    },
    pointTableLoadEvent() {
      if (
        this.pageParams.total >
        this.pageParams.pageNo * this.pageParams.pageSize
      ) {
        this.pageParams.pageNo++
        this.getTaskPointTableData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/module.scss";
.inspDetails {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-list {
    padding: 0px 16px;
    position: relative;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #b0e3fa;
        line-height: 30px;
        width: 100px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 30px;
        max-width: 210px;
      }
    }
    .view-more {
      position: absolute;
      right: 10px;
      top: 8px;
      font-size: 14px;
      color: #8bddf5;
      cursor: pointer;
    }
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
}
</style>
