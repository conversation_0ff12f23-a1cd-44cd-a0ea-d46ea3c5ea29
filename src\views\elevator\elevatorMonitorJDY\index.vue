<!--
 * @Author: hedd
 * @Date: 2023-07-04 14:35:15
 * @LastEditTime: 2025-05-01 15:09:31
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\elevatorMonitor\index.vue
 * @Description:
-->
<template>
  <div class="content">
    <div v-if="NODE_ENV_FLAG">
      <el-button class="sino-button-sure" @click="szzlyyChange('build')">SZZLYYBuild</el-button>
      <el-button class="sino-button-sure" @click="testAlarm('build')">测试打开报警详情</el-button>
      <el-button class="sino-button-sure" @click="testDeviceDetails('build')">测试打开报警详情</el-button>
    </div>
    <div ref="collapseWidth" class="right-content">
      <div v-show="collapseFlag" class="bg-title">
        <!-- 表头 -->
        <div v-scrollMove class="bg-tab">
          <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex == 'verticalLadder' }" @click="activeTypeEvent('verticalLadder')">直梯监测</div>
          <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex == 'escalator' }" @click="activeTypeEvent('escalator')">扶梯监测</div>
        </div>
      </div>
      <div style="width: 100%; height: 100%; overflow: hidden">
        <div v-show="collapseFlag" class="bg-content room-info-box">
          <div class="sys-box">
            <div class="tabActive">
              <div v-scrollMove style="display: flex;overflow: hidden;">
                <div
                  v-for="(i, index) in activeOption.filter(v => v.is.includes(activeTabIndex))"
                  :key="index"
                  class="tabItem"
                  :class="childrenActive == i.id ? 'selected' : ''"
                  @click="changeActive(i)">
                  {{ i.name }}
                </div>
              </div>
              <el-dropdown trigger="click" @command="tabItemCommand">
                <img src="@/assets/images/qhdsys/bg-gd.png" width="24px" height="24px" alt="" />
                <el-dropdown-menu slot="dropdown" class="dropdown">
                  <el-dropdown-item
                    v-for="(item, index) in activeOption.filter(v => v.is.includes(activeTabIndex))"
                    :key="index"
                    :command="item.id"
                    :class="{ isBjxl: childrenActive === item.id }"
                  >{{ item.name }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="tab-content">
              <component
                :is="currentComponent"
                :key="componentKey"
                :ref="currentComponent"
                type="device"
                :roomData="roomData"
                @roomEvent="roomEvent"
                @changeDateType="setWPFMessage"
              ></component>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible="alarmDetailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="() => alarmDetailVisible = false"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="alarmDetailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
    <DeviceDetails v-if="isDeviceDetails" :dialogShow="isDeviceDetails" :roomData="roomData" :deviceId="deviceId" @deviceDetailsClose="() => isDeviceDetails = false" />
    <!-- 巡检详情 -->
    <template v-if="inspManageShow">
      <inspManage
        ref="inspManage"
        :dialogData="dialogData.deviceInfo"
        :dialogShow="inspManageShow"
        @configCloseDialog="configCloseDeviceDialog('inspManageShow')"
      />
    </template>
  </div>
</template>
<script>
import  EmergencyDetail  from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
import { GetMonitoringItemsList } from '@/utils/spaceManage'
export default {
  name: 'elevatorMonitor',
  components: {
    MonitorOverview: () => import('../components/monitorOverview/index'),
    ElevatorRecord: () => import('../components/monitorOverview/components/elevatorRecord'),
    MonitorStatistics: () => import('../components/monitorStatistics/index'),
    OpenDoorAnalysis: () => import('../components/openDoorAnalysis/index'),
    StopAnalysis: () => import('../components/stopAnalysis/index'),
    EmergencyDetail,
    DeviceDetails: () => import('@/views/spaceManage/components/deviceDetailsNew.vue'),
    RepairComponent: () => import('@/views/spaceManage/sysTypeComponent/repairComponent'),
    IcisComponent: () => import('@/views/spaceManage/sysTypeComponent/icisComponent'),
    UpkeepComponent: () => import('@/views/spaceManage/sysTypeComponent/upkeepComponent'),
    inspManage: () => import('@/views/spaceManage/sysTypeComponent/inspManage') // 巡检详情
  },
  data() {
    const NODE_ENV_FLAG = import.meta.env.DEV
    return {
      NODE_ENV_FLAG,
      collapseFlag: true,
      activeTabIndex: 'verticalLadder',
      roomData: {
        projectCode: 'DTXT'
      },
      currentAlarmId: '',
      alarmDetailVisible: false,
      isDeviceDetails: false,
      deviceId: '',
      childrenActive: 'monitoringOverview',
      activeOption: [
        {
          id: 'monitoringOverview',
          name: '监测总览',
          domId: 'MonitorOverview',
          is: ['verticalLadder', 'escalator']
        },
        {
          id: 'elevatorAlarmRecord',
          name: '报警记录',
          domId: 'ElevatorRecord',
          is: ['verticalLadder', 'escalator']
        },
        {
          id: 'maintain',
          name: '维修',
          domId: 'RepairComponent',
          is: ['verticalLadder', 'escalator']
        },
        {
          id: 'inspection',
          name: '巡检',
          domId: 'IcisComponent',
          is: ['verticalLadder', 'escalator']
        },
        {
          id: 'upkeep',
          name: '保养',
          domId: 'UpkeepComponent',
          is: ['verticalLadder', 'escalator']
        },
        {
          id: 'operationStatistics',
          name: '运行统计',
          domId: 'MonitorStatistics',
          is: ['verticalLadder']
        },
        {
          id: 'doorOpeningAnalysis',
          name: '开门分析',
          domId: 'OpenDoorAnalysis',
          is: ['verticalLadder']
        },
        {
          id: 'dockingStatistics',
          name: '停靠统计',
          domId: 'StopAnalysis',
          is: ['verticalLadder']
        }
      ],
      dialogData: {
        deviceInfo: {}, // 设备视角数据
        deviceDetailInfo: {}, // 变配电设备详情
        requestParams: {
          projectCode: '',
          spaceId: '', // 空间id
          ssmType: '' // 空间类型
        },
        paramData: {},
        currentAlarmId: '', // 报警详情id
        entrySiftData: {} // 入口筛查数据
      },
      inspManageShow: false,
      // UI状态
      uiState: {
        collapseFlag: true // 折叠状态
      },
      componentKey: new Date().getTime()
    }
  },
  computed: {
    // 当前菜单组件
    currentComponent() {
      return this.activeOption.find((e) => e.id === this.childrenActive)?.domId || 'MonitorOverview'
    }
  },
  created () {
    // 初始化跳转路由地址及参数 elevatorMonitor?areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}&time=2024/5/28 14:42:26
    if (this.$route.query.areaData) {
      GetMonitoringItemsList({
        page: 1,
        pageSize: 999,
        sysOfCode: this.$route.query.projectCode || 'DTXT',
        spaceId: this.$route.query.ssmCodes?.split(',')?.at(-1)
      }).then(res => {
        const {code, data} = res.data
        if (code == '200') {
          Object.assign(this.roomData, {
            ...JSON.parse(this.$route.query.areaData),
            ssmCodes: this.$route.query.ssmCodes,
            projectCode: this.$route.query.projectCode || 'DTXT',
            deviceId: data.records.map(i => i.assetsId).join(',')
          })
        }
      })
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        let eventData = JSON.parse(event.data)
        console.log('wpf消息: ', eventData)
        if (eventData.type == 'area') {
          GetMonitoringItemsList({
            page: 1,
            pageSize: 999,
            sysOfCode: eventData.projectCode || this.roomData.projectCode,
            spaceId: eventData.ssmCodes?.split(',')?.at(-1)
          }).then(res => {
            const {code, data} = res.data
            if (code == '200') {
              Object.assign(this.roomData, {
                ssmCodes: eventData.ssmCodes,
                deviceId: data.records.map(i => i.assetsId).join(',')
              })
              this.componentKey = new Date().getTime()
              let dateType = ''
              if (this.childrenActive == 'elevatorAlarmRecord') {
                dateType = 'day'
              } else if (this.childrenActive == 'maintain') {
                dateType = 'week'
              } else if (this.childrenActive == 'inspection' || this.childrenActive == 'upkeep') {
                dateType = 'month'
              }
              const wpfParams = {
                elevatorType: this.activeTabIndex, // verticalLadder 直梯 escalator 扶梯
                statisticalItem: this.childrenActive,
                dateType
              }
              this.setWPFMessage(wpfParams)
            }
          })
        }
      })
    } catch (errpr) { }
  },
  methods: {
    setWPFMessage(data) {
      const sendData = {
        btnType: data?.typeName || data?.dateType || '',
        elevatorType: data.elevatorType || this.activeTabIndex,
        statisticalItem: data.statisticalItem || this.childrenActive,
        spaceId: this.roomData.ssmCodes.split(',').at(-1) || this.$route.query.ssmCodes?.split(',')?.at(-1)
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.ElevatorMenuName(JSON.stringify(sendData))
      } catch (error) {}
    },
    changeActive(val) {
      this.childrenActive = val.id
      let dateType = ''
      if (val.id == 'elevatorAlarmRecord') {
        dateType = 'day'
      } else if (val.id == 'maintain') {
        dateType = 'week'
      } else if (val.id == 'inspection' || val.id == 'upkeep') {
        dateType = 'month'
      }
      const wpfParams = {
        elevatorType: this.activeTabIndex, // verticalLadder 直梯 escalator 扶梯
        statisticalItem: this.childrenActive,
        dateType
      }
      this.setWPFMessage(wpfParams)
    },
    tabItemCommand(val) {
      this.childrenActive = val
    },
    roomEvent(data) {
      if (data.type === 'insp') {
        let inspInfo = {
          type: data.type,
          taskType: data.taskType || 'normal',
          assetId: data.assetId,
          assetName: data.assetName,
          menu: data.menu
        }
        if (data.autoJump) {
          inspInfo.autoJump = true
        }
        this.setDeviceInfoEvent(inspInfo)
      }
    },
    setDeviceInfoEvent(deviceInfo) {
      this.dialogData.deviceInfo = {
        ...this.roomData,
        ...deviceInfo,
        deviceId: deviceInfo.assetId,
        deviceName: deviceInfo.assetName,
        modelCode: deviceInfo.modelCode,
        detailsNav: deviceInfo.detailsNav
      }
      if (this.uiState.collapseFlag) {
        this.uiState.collapseFlag = !this.uiState.collapseFlag
        this.$nextTick(() => {
          this.$refs.collapseWidth.style.width = '0'
          this.$refs.collapseWidth.style.padding = '0'
        })
      }
      if (deviceInfo.type == 'insp') {
        this.dialogData.deviceInfo.taskType = deviceInfo.taskType
        this.inspManageShow = true
      } else {
        this.dialogState.deviceInfoManagementShow = true
      }
    },
    configCloseDeviceDialog() {
      this.$nextTick(() => {
        this.inspManageShow = false
        setTimeout(() => {
          this.uiState.collapseFlag = true
        }, 200)
        this.$refs.collapseWidth.style.width = '24.573%'
        this.$refs.collapseWidth.style.padding = '0 25px 10px 35px'
      })
    },
    testDeviceDetails () {
      this.isDeviceDetails = true
      this.deviceId = '1916338906023215106'
    },
    activeTypeEvent(val) {
      this.activeTabIndex = val
      if (val == 'verticalLadder') {
        this.roomData.projectCode = 'DTXT'
      } else {
        this.roomData.projectCode = 'FTJC'
      }
      this.childrenActive = 'monitoringOverview'
      this.componentKey = new Date().getTime()
      const wpfParams = {
        elevatorType: this.activeTabIndex, // verticalLadder 直梯 escalator 扶梯
        statisticalItem: this.childrenActive
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.ElevatorMenuName(JSON.stringify(wpfParams))
      } catch (error) {}
    },
    testAlarm () {
      this.currentAlarmId = 'BJ2024951441501725518510496'
      this.alarmDetailVisible = true
    },
    szzlyyChange (type) {
      let paramsData = {}
      if (type === 'build') {
        paramsData = {
          areaData: {
            ssmType: '3',
            parentId: '1724753193365172224',
            ssmName: '第一住院楼',
            childList: ['1724753193415503862', '1724753193415503863', '1724753193415503864', '1724753193415503865', '1724753193415503866', '1724753193415503867', '1724753193415503868', '1724753193415503869', '1724753193415503870', '1724753193415503871', '1724753193415503872', '1724753193415503873', '1724753193415503874']
          }
        }
      }
      Object.assign(this.roomData, {
        ...paramsData
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 4px;
  box-sizing: border-box;
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 24.573%;
    height: 100%;
    margin: 0 0 0 auto;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 25px 10px 35px;
    transition: width 0.3s linear;
    overflow: hidden;
    .bg-title {
      width: 100%;
      padding: 0;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      color: #dceaff;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      // 不可被选中
      -webkit-user-select: none;
      .bg-tab {
        width: 100%;
        display: flex;
        overflow: hidden;
        box-sizing: border-box;
        justify-content: center;
        .tab-div {
          width: 100%;
          flex: 1;
          height: 40px;
          flex-shrink: 0;
          line-height: 40px;
          text-align: center;
          font-size: 16px;
          color: #a4acb9;
          background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
          background-size: 100% 100%;
        }
        .tab-div:hover {
          cursor: pointer;
        }
        .is-activeTab {
          color: #b0e3fa;
          background: url('@/assets/images/qhdsys/bg-tab-xz.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      ::v-deep .el-popover {
        width: fit-content;
        min-width: 0;
        background: #374b79;
      }
      .center-empty {
        flex: 1;
        background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
        background-size: 100% 100%;
      }
      .icon-collapse {
        width: 48px;
        height: 40px;
        // flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
        &:hover {
          cursor: pointer;
        }
        img {
          margin: auto;
        }
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      //   padding: 10px 2px 2px 2px;
      //   width: 100%;
      //   height: calc(100% - 40px);
      width: calc(100% + 0px);
      height: calc(100% - 40px);
      overflow-y: auto;
      overflow-x: hidden;
      /* scroll-view 不显示滚动条 */
    }
    .bg-content::-webkit-scrollbar {
      height: 0;
      width: 0;
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .box-type {
        width: 100%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        span {
          display: inline-block;
          width: fit-content;
          height: 24px;
          padding: 0 5px;
          background-color: #24396d;
          text-align: center;
          line-height: 24px;
          color: #dceaff;
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          cursor: pointer;
          margin: 5px 2px 0 2px;
        }
        .type_active {
          color: #ffe3a6;
          background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .sys-box {
        width: 100%;
        height: 100%;
        .tabActive {
          background-color: rgba($color: #8591CE, $alpha: 0.15);
          padding: 0 8px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          overflow: hidden;
          box-sizing: border-box;
          .tabItem {
            margin-right: 8px;
            background-color: rgba($color: #8591CE, $alpha: 0.15);
            font-size: 14px;
            color: #B0E3FA;
            height: 26px;
            line-height: 26px;
            padding: 0 12px;
            text-align: center;
            flex-shrink: 0;
            cursor: pointer;
          }
          .selected {
            background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat center;
            background-size: 100% 100%;
            color: #8BDDF5;
          }
        }
        .tab-content {
          width: 100%;
          height: calc(100% - 50px);
        }
      }
    }
  }
  .roomInfoManagement {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;

  }
  .sino-button-sure {
    z-index: 9999;
  }
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
