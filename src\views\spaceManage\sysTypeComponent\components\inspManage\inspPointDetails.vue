<template>
  <div v-loading="pageLoading" class="inspPointDetails">
    <ModuleCard :showTitle="false" class="module-container" style="height: 15%; background: rgba(133,145,206,0.15);">
      <div slot="content" class="module-content info-list" style="height: 100%">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p v-if="item.key == 'taskPointName'" class="item-label">{{ menuName + item.label }}：</p>
          <p v-else class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ pointInfo[item.key] || '---' }}</p>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard :showTitle="false" class="module-container" style="height: 85%">
      <div slot="content" class="module-content" style="height: 100%;">
        <el-table
          class="table-center-transfer"
          :data="tableData"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: 'rgba(133,145,206,0.08)!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%;background: rgba(133,145,206,0.15);"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column fixed prop="detailName" show-overflow-tooltip :label="menuName + '内容'">
            <template slot-scope="scope" :class="scope.row">
              <span v-if="scope.row.isNum == '0'"
              >正常值：{{ scope.row.rangeStart + '-' + scope.row.rangeEnd + ' ' + scope.row.einheitName
              }}{{ scope.row.contentStandard ? '（当前' + scope.row.contentStandard + ' ' + scope.row.einheitName + '）' : '' }}</span
              >
              <span v-if="scope.row.isNum == '3' || scope.row.isNum == '2'">
                <span>{{ scope.row.contentStandard || '无' }}</span>
              </span>
              <!--  巡检选项为'无' -->
              <span v-if="scope.row.isNum == '1'">无</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="content" show-overflow-tooltip :label="menuName + '标准'"></el-table-column>
        </el-table>

        <div style="background: rgba(133,145,206,0.15);margin-top: 6px;">
          <div class="info-header">
            <p class="info-header-text">{{ menuName }}执行</p>
          </div>
          <div class="info-list">
            <div class="info-list-item">
              <p class="item-label">{{ menuName }}情况：</p>
              <p v-if="dialogData.menu == 'icis'" class="item-value">{{ excute.carryOutFlag == '0' ? '未巡' : excute.carryOutFlag == '1' ? '已巡' : '---' }}</p>
              <p v-else class="item-value">{{ excute.carryOutFlag == '0' ? '未保' : excute.carryOutFlag == '1' ? '已保养' : '---' }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">执行人员：</p>
              <p class="item-value">{{ excute.implementPersonName || '---' }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">实际{{ menuName }}时间：</p>
              <p class="item-value">{{ excute.excuteTime || '---' }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">定位状态：</p>
              <p class="item-value">{{ excute.spyScan || '---' }}</p>
            </div>
          </div>
        </div>

        <div style="background: rgba(133,145,206,0.15);margin-top: 6px;">
          <div class="info-header">
            <p class="info-header-text">{{ menuName }}结果</p>
          </div>
          <div class="info-list">
            <div class="info-list-item">
              <p class="item-label">结果：</p>
              <p class="item-value">{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : (dialogData.menu == 'icis' ? '未巡检' : '未保养') }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">描述：</p>
              <p class="item-value">{{ result.desc || '---' }}</p>
            </div>
            <div class="info-list-item">
              <p class="item-label">语音：</p>
              <p class="item-value">
                <audio v-if="result.callerTapeUrl" ref="player" style="height: 30px;" :src="result.callerTapeUrl" preload="true" controls="controls"></audio>
                <span v-else>暂无</span>
              </p>
            </div>
            <div class="info-list-item">
              <p class="item-label">图片：</p>
              <p v-if="result.attachmentUrlList && result.attachmentUrlList.length" class="item-value">
                <span v-for="(img, index) in result.attachmentUrlList" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                </span>
              </p>
              <p v-else class="item-value">暂无</p>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <div class="module-footer" style="">
      <el-button @click="switchFun('prev')">上一个</el-button>
      <el-button @click="switchFun('next')">下一个</el-button>
    </div>
  </div>
</template>

<script>
import { getInspPointDetail } from '@/utils/centerScreenApi'
export default {
  name: 'inspPointDetails',
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    activePoint: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      menuName: '',
      infoData: {
        workContent: [1, 2, 3, 4, 5]
      },
      infoList: [
        {label: '点名称', key: 'taskPointName'},
        {label: '所属区域', key: 'regionName'}
      ],
      typeOptions: {
        8: '单次',
        6: '每日',
        0: '每周',
        2: '每月',
        3: '季度',
        5: '全年',
        7: '自定义'
      },
      pointInfo: {},
      tableData: [],
      taskPoint: '',
      excute: '',
      result: '',
      pageLoading: false,
      currentIndex: this.activePoint.currentIndex
    }
  },
  computed: {

  },
  created() {
    this.menuName = this.dialogData.menu == 'icis' ? '巡检' : '保养'
    this.getDataDetail(this.activePoint.pointIds[this.activePoint.currentIndex])
  },
  methods: {
    switchFun(type) {
      if (type === 'next') {
        this.currentIndex = this.currentIndex + 1 < this.activePoint.pointIds.length ? this.currentIndex + 1 : 0
      } else if (type === 'prev') {
        this.currentIndex = this.currentIndex - 1 >= 0 ? this.currentIndex - 1 : this.activePoint.pointIds.length - 1
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.PatrolRealView(JSON.stringify({
          type: 'point',
          id: this.activePoint.pointIds[this.currentIndex]
        }))
      } catch (error) {}
      this.getDataDetail(this.activePoint.pointIds[this.currentIndex])
    },
    getDataDetail(id) {
      this.pageLoading = true
      getInspPointDetail({ id }).then((res) => {
        this.pageLoading = false
        const data = res.data
        if (data.code === '200') {
          Object.assign(this, {
            taskPoint: data.data.taskPoint,
            tableData: data.data.project.projectdetailsReleaseList,
            excute: data.data.excute,
            result: data.data.result,
            pointInfo: {
              regionName: data.data.taskPoint.particulars ? JSON.parse(data.data.taskPoint.particulars).regionName : '',
              taskPointName: data.data.taskPoint.taskPointName || ''
            }
          })
          if (this.tableData && this.tableData.length) {
            this.tableData.forEach((val, index) => {
              if (val.isNum === '3') {
                val.radioTextArr = JSON.parse(val.termJson)
                // console.log(val.radioTextArr)
              }
            })
          }
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../../../style/module.scss';
.inspPointDetails {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .info-header-status {
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      padding: 3px 8px;
      border-radius: 100px;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 100px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
        flex: 1;
        img {
          margin-top: 10px;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
  .module-footer {
    text-align: center;
    padding: 10px 0px 0px 0px;
    .el-button {
      background-image: url("@/assets/images/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 35px;
      width: 45%;
    }
  }
}
</style>
