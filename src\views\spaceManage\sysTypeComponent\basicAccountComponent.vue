<template>
  <div class="basicAccountComponent">
    <!-- <div class="module-container" style="height: 100%"> -->
    <div class="module-container">
      <div v-if="hasTitle" class="module-header">
        <div class="title-left">
          <!-- <p class="title-left-icon"></p> -->
          <p class="title-left-text">基础信息</p>
        </div>
        <div v-if="isMonitor" class="title-right" @click="moreInfoChange(true)">
          <!-- <span class="viewMore" @click="ShowDialog">更多<i class="el-icon-arrow-right"></i></span> -->
          更多信息
        </div>
      </div>
      <div
        v-scrollbarHover
        class="module-content"
        :style="{
          height: hasTitle ? 'calc(100% - 2.8rem)' : '100%',
          'overflow-y': 'auto',
        }"
      >
        <table class="base-table">
          <tr v-for="(item, index) in baseInfo" :key="index" class="base-tr">
            <td class="label-td">{{ item.label }}</td>
            <td v-if="item.key == 'onlineStatus'" class="value-td">
              {{ assetDetailsData[item.key] == 1 ? '在线' : '离线' }}
            </td>
            <td v-else-if="item.key == 'alarmStatus'" class="value-td">
              {{ assetDetailsData[item.key] == 1 ? '报警' : '正常' }}
            </td>
            <td v-else-if="item.key == 'XYZ'" class="value-td">
              X:{{ assetDetailsData?.modelPositionX ?? '-'}},
              Y:{{ assetDetailsData?.modelPositionY ?? '-' }},
              Z:{{ assetDetailsData?.modelPositionZ ?? '-' }}
            </td>
            <td v-else class="value-td">
              {{ (assetDetailsData[item.key] || "---") + (item.unit || "") }}
            </td>
          </tr>
        </table>
      </div>
      <!-- <div class="divider"></div> -->
    </div>
    <div v-if="roomData.tabName == 'SpaceChair'" class="module-container">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">椅位使用统计</p>
        </div>
        <div class="title-right">
          <el-date-picker v-model="usageDate" type="date" placeholder="选择日期" :clearable="false" popper-class="date-style" value-format="yyyy-MM-dd" :editable="false" />
          <i class="el-icon-arrow-down" style="margin-left: 4px;"></i>
        </div>
      </div>
      <div
        v-scrollbarHover
        class="module-content"
        style="height: 'calc(100% - 2.8rem)'; overflow-y: 'auto'"
      >
        <div class="monitor-list">
          <div v-for="(item, index) in usageList" :key="index" class="monitor-list-item" style="width: calc(100% / 3)">
            <p class="item-name">{{ item.name }}</p>
            <p class="item-value" :style="{color: item.color}">{{ usageData[item.key] | formatSeconds}}</p>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isMonitor" class="module-container">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">运行监测</p>
        </div>
        <div class="title-right" @click="paramDetailsChange(true)">
          历史记录
        </div>
      </div>
      <div
        v-scrollbarHover
        class="module-content"
        style="height: 'calc(100% - 2.8rem)'; overflow-y: 'auto'"
      >
        <div class="monitor-list">
          <div v-for="(item, index) in monitorList" :key="index" class="monitor-list-item">
            <p class="item-name">{{ item.metadataName }}</p>
            <p class="item-value" :style="{color: item.sectionColor}">{{ item.valueText || '-' }}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 参数详情 -->
    <paramDetails v-if="isParamDetails" :dialogShow="isParamDetails" :paramData="assetDetailsData" @paramDetailsClose="paramDetailsChange" />
    <!-- 设备详情 -->
    <deviceDetails v-if="isDeviceDetails" :dialogShow="isDeviceDetails" :roomData="roomData" :deviceId="deviceId" @deviceDetailsClose="moreInfoChange(false)"/>
    <!-- <div class="module-container" style="height: calc(30% - 16px)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">设备二维码</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem); text-align: center;">
        <el-image fit="contain" :src="deviceQRCode" :preview-src-list="[deviceQRCode]"></el-image>
      </div>
    </div>
    <div class="divider" style="margin: 15px 0px 0px 0px"></div>
    <div class="module-container" style="height: 30%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">设备生命周期</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div class="lifeCycle-content" v-if="deviceLifeCycleList.length">
          <div class="lifeCycle-item" v-for="(item, index) in deviceLifeCycleList" :key="item.id">
            <p class="lifeCycle-name" :style="{color: nameColor(item.disposeModeCode)}">{{ item.disposeModeName }}</p>
            <p class="lifeCycle-line"></p>
            <p class="lifeCycle-date">{{ item.disposeDate.slice(0, 10) }}</p>
            <span class="lifeCycle-current" v-if="item.status == 0 && index !== 0">当前</span>
          </div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div> -->
    <!-- <template v-if="assetDetailsShow">
      <equipmentDetailTable :dialogShow="assetDetailsShow" :deviceId="deviceId" @configCloseDialog="closeDialog"></equipmentDetailTable>
    </template> -->
  </div>
</template>

<script>
// import equipmentDetailTable from '../components/equipmentDetail.vue'
import { GetDeviceBasicInfo, GetMonitorAssetInfo, GetMonitoringItemsParams, GetChairUseStat } from '@/utils/spaceManage'
import paramDetails from './components/paramDetails.vue'
import deviceDetails from '../components/deviceDetailsNew.vue'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'basicAccountComponent',
  // components: { equipmentDetailTable },
  components: { paramDetails, deviceDetails },
  // components: { deviceDetails },
  filters: {
    formatSeconds(seconds) {
      if (!seconds) return '-'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours ?? 0}.${minutes ?? 0}小时`
    }
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    },
    hasTitle: {
      type: Boolean,
      default: true
    },
    roomData: {
      type: Object,
      default: () => {}
    },
    isMonitor: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      assetDetailsShow: false,
      assetDetailsData: {}, // 资产详情
      deviceQRCode: '', // 设备二维码
      deviceLifeCycleList: [], // 设备生命周期列表
      monitorList: [],
      usageList: [
        { name: '空闲时长', key: 'freeCount', color: '#61E29D' },
        { name: '正常使用时长', key: 'normalCount', color: '' },
        { name: '异常占用时长', key: 'exceptionalCount', color: '#FF2D55' }
      ],
      usageData: {},
      usageDate: moment().format('YYYY-MM-DD'),
      isParamDetails: false,
      isDeviceDetails: false
    }
  },
  computed: {
    nameColor() {
      return (val) => {
        if (val !== '6' && val !== '18') {
          return '#7EAEF9'
        } else if (val === '18') {
          return '#F5666D'
        } else if (val === '6') {
          return '#7BFFC1'
        }
      }
    },
    baseInfo() {
      // if (['OperatinRoom', 'HazMat', 'LogisticsVehicle'].includes(this.roomData.tabName)) {
      if (this.isMonitor) {
        return [
          { label: '设备名称', key: 'assetsName' },
          { label: '设备编码', key: 'assetsCode' },
          { label: '通用名称', key: 'commonName' },
          { label: '所属品类', key: 'sysOfName' },
          { label: '模型Id', key: 'modelCode' },
          { label: '模型坐标', key: 'XYZ' },
          { label: '所在位置', key: 'spaceLocationName' },
          { label: '管理部门', key: 'useDeptName' },
          { label: '责任人', key: 'principalName' },
          { label: '责任人电话', key: 'principalPhone' }
        ]
      } else {
        return [
          { label: '设备名称', key: 'assetName'},
          { label: '设备编码', key: 'assetCode'},
          { label: '品牌', key: 'assetBrand' },
          { label: '型号', key: 'assetModel'},
          { label: '生产日期', key: 'dateOfManufacture' },
          { label: 'SN码', key: 'assetSn' },
          { label: '所在区域', key: 'regionName' },
          { label: '计量单位', key: 'unitOfMeasurement' },
          { label: '启用日期', key: 'startDate' },
          // 假字段 无实际值
          { label: '金额', key: 'amountOfMoney'},
          { label: '使用期限', key: 'serviceLife', unit: '月' },
          { label: '资产状态', key: 'assetStatusName' },
          { label: '归口部门', key: 'centralizedDepartmentName' },
          { label: '资产大类', key: 'assetCategoryName' },
          { label: '资产小类', key: 'assetSubcategoryName' },
          { label: '专业类别', key: 'professionalCategoryName'},
          { label: '系统类别', key: 'systemCategoryName' },
          { label: '模型编码', key: 'modelCode'
          }
        ]
      }
    }
  },
  watch: {
    deviceId: {
      handler(newStr, oldStr) {
        if (this.isMonitor) {
          this.getMonitorAssetInfo()
          this.getMonitoringItemsParams()
          if (this.roomData.tabName == 'SpaceChair') {
            this.getChairUseStat()
          }
        } else {
          this.getDeviceBasicInfo()
        }
      },
      immediate: true
    },
    usageDate(val) {
      this.getChairUseStat()
    }
  },
  mounted() {
    console.log('设备详情', this.deviceId, this.roomData)
    // this.getDeviceBasicInfo()
  },
  methods: {
    moreInfoChange(val = true) {
      this.isDeviceDetails = val
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    paramDetailsChange (val = true) {
      console.log('paramDetailsChange', val)
      this.isParamDetails = val
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    // 获取椅位使用统计
    getChairUseStat() {
      let params = {
        monitorDeviceId: this.deviceId,
        dateType: 'day',
        sTime: this.usageDate + ' 00:00:00',
        eTime: this.usageDate + ' 23:59:59'
      }
      GetChairUseStat(params).then(res => {
        if (res.data.code == 200) {
          this.usageData = res.data.result
        }
      })
    },
    // 获取监测参数
    getMonitoringItemsParams() {
      GetMonitoringItemsParams({assetsId: this.deviceId}).then((res) => {
        if (res.data.code == 200) {
          this.monitorList = res.data.data
        }
      })
    },
    // 获取资产详情
    getMonitorAssetInfo() {
      GetMonitorAssetInfo({assetsId: this.deviceId}).then(res => {
        if (res.data.code == 200) {
          this.assetDetailsData = res.data.data
        } else {
          this.assetDetailsData = {}
        }
      })
    },
    // 获取资产详情
    getDeviceBasicInfo() {
      GetDeviceBasicInfo({ assetsId: this.deviceId }).then((res) => {
        if (res.data.code === '200') {
          this.assetDetailsData = res.data.data
          console.log(this.assetDetailsData)
          localStorage.setItem(
            'imesAssetsData',
            JSON.stringify({
              assetsNumber: this.assetDetailsData.assetsId,
              id: this.assetDetailsData.assetsId
            })
          )
          // this.$emit('roomEvent', {
          //   type: 'basicAccount',
          //   deviceName: this.assetDetailsData.assetName
          // })
        } else {
          this.assetDetailsData = {}
          localStorage.setItem(
            'imesAssetsData',
            JSON.stringify({
              assetsNumber: '',
              id: ''
            })
          )
          // this.$emit('roomEvent', {
          //   type: 'basicAccount',
          //   deviceName: ''
          // })
        }
      })
    },
    ShowDialog() {
      this.assetDetailsShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    closeDialog() {
      this.assetDetailsShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(
          false
        )
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../style/module.scss";
.basicAccountComponent {
  width: 100%;
  height: 100%;
  overflow: auto;
  .base-table {
    border: 1px solid rgba(168, 172, 171, 0.08);
    width: 100%;

    tr:nth-child(even) {
      // background-color: rgba(133,145,206,0.15);
    }
    .base-tr {
      width: 100%;
      td {
        line-height: 20px;
        padding: 9px 0;
      }
      td:nth-child(1) {
        color: #b0e3fa;
      }
      .label-td {
        width: 75px;
        padding-left: 40px;
        text-align: left;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #7eaef9;
        white-space: nowrap;
        vertical-align: top;
      }
      .value-td {
        text-align: left;
        padding-left: 20px !important;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
  .divider {
    height: 1px;
    width: 100%;
    background: #2e4989;
  }
  ::v-deep .el-image {
    height: 100%;
    .el-image__error {
      background: transparent !important;
    }
  }
  .lifeCycle-content {
    height: 100%;
    display: flex;
    align-items: center;
    overflow: auto;
    .lifeCycle-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      position: relative;
      .lifeCycle-name {
        font-size: 14px;
        min-height: 19px;
        line-height: 19px;
      }
      .lifeCycle-line {
        margin: 15px 0px;
        width: 100%;
        height: 1px;
        background: #494b5d;
        position: relative;
        &::after {
          content: "";
          width: 20px;
          height: 1px;
          background: #494b5d;
          position: absolute;
          right: -20px;
          top: 0px;
        }
      }
      .lifeCycle-date {
        min-height: 19px;
        line-height: 19px;
        min-width: 80px;
        text-align: center;
        font-size: 12px;
        color: #ffe3a6;
        border-radius: 2px;
        background: rgba(255, 227, 166, 0.14);
      }
      .lifeCycle-current {
        display: inline-block;
        font-size: 14px;
        color: #f5666d;
        position: absolute;
        left: -24px;
        top: 0;
        min-height: 19px;
        line-height: 19px;
        &::before {
          content: "";
          position: absolute;
          width: 7px;
          height: 7px;
          background: #ffe3a6;
          border-radius: 50%;
          left: 10px;
          top: 31px;
          z-index: 1;
        }
      }
      &::before {
        content: "";
        position: absolute;
        width: 7px;
        height: 7px;
        background: #ffe3a6;
        border-radius: 50%;
        left: 36px;
        top: 31px;
        z-index: 1;
      }
      &::after {
        content: "";
        position: absolute;
        height: 15px;
        width: 1px;
        background: #ffe3a6;
        left: 39px;
        top: 35px;
        z-index: 1;
      }
    }
    .lifeCycle-item:last-child {
      .lifeCycle-line {
        &::before {
          content: "";
          position: absolute;
          width: 8px;
          height: 8px;
          border-top: 1px solid #494b5d;
          border-right: 1px solid #494b5d;
          transform: rotate(45deg);
          right: -17px;
          top: -4px;
        }
      }
    }
  }
}
.module-header {
  // margin: 10px 0;
  margin-top: 5px;
  width: 100%;
  background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
  background-size: contain;

  .title-left {
    padding-left: 30px;
  }
  .title-right {
    font-size: 14px;
    align-items: center;
    cursor: pointer;
    margin-right: 10px;
    ::v-deep .el-date-editor {
      width: 90px;
      .el-input__inner {
        cursor: pointer;
        padding: 0px;
        line-height: 20px;
        border: none;
        text-align: right;
      }
      .el-input__prefix, .el-input__suffix {
        display: none;
      }
    }
  }
}
.monitor-list {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .monitor-list-item {
    width: calc(25%);
    // width: calc(25% - 7.5px);
    padding: 10px 0px;
    flex-shrink: 0;
    // margin-right: 10px;
    margin-bottom: 10px;
    text-align: center;
    background: #8591ce0d;
    .item-name{
      color: #fff;
      font-size: 14px;
    }
    .item-value{
      color: #D6EFF1;
      font-size: 16px;
      margin-top: 10px;
      word-break: break-all;
    }
  }
  .monitor-list-item:nth-child(4n){
    margin-right: 0px;
  }
}

.sys-box-content {
  padding: 0 !important;
}
</style>
