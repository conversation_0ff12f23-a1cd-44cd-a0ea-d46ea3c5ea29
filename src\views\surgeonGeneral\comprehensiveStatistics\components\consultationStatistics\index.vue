<template>
  <div class="echarts-rightBottom-left">
    <BgTitle showMore @moreClick="showDetail">
      <template #title>
        <div>
          <span style="margin-right: 10px">就诊统计</span>
          <el-dropdown trigger="click" @command="deptChange">
            <span class="el-dropdown-link" style="color: #b0e3fa">
              {{ deptList.find((v) => v.id == deptId)?.deptName ?? "" }}
              <i class="el-icon-caret-bottom"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in deptList"
                :key="item.id"
                :command="item.id"
                :class="{ isBjxl: deptId == item.id }"
              >
                {{ item.deptName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </template>
      <template #right>
        <el-date-picker
          v-model="date"
          class="datePickerInput"
          popper-class="date-style"
          type="date"
          value-format="yyyy-MM-dd"
          :clearable="false"
          @change="dateChange"
        >
        </el-date-picker>
      </template>
    </BgTitle>
    <div id="boxed" class="bg-content" @mouseenter="autoRoll('stop')" @mouseleave="autoRoll()">
      <el-table
        ref="table"
        v-loading="tableLoading"
        stripe
        :data="tableData"
        height="100%"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        element-loading-background="rgba(0, 0, 0, 0.2)"

      >
        <el-table-column prop="departmentName" label="科室名称" show-overflow-tooltip width="95px">
        </el-table-column>
        <el-table-column
          prop="numberTotal"
          show-overflow-tooltip
          label="初诊复诊数"
          width="110px"
        ></el-table-column>
        <!-- 初诊预约数/复诊预约数 -->
        <el-table-column
          prop="firstVisitNum"
          show-overflow-tooltip
          label="初诊预约数"
          width="110px"
        ></el-table-column>
        <el-table-column
          prop="returnVisitNum"
          show-overflow-tooltip
          label="复诊预约数"
          width="110px"
        ></el-table-column>
        <el-table-column
          prop="registerNum"
          show-overflow-tooltip
          label="挂号数"
          width="80px"
        ></el-table-column>
        <el-table-column
          prop="surplusNum"
          label="剩余号量"
          width="95px"
        ></el-table-column>
        <el-table-column prop="waitNum" label="候诊数" width="80px">
        </el-table-column>
        <el-table-column prop="alreadyNum" label="已诊人数" width="95px">
        </el-table-column>
        <el-table-column prop="avgmin" label="平均就诊时长" width="120px">
        </el-table-column>
      </el-table>
    </div>
    <ConsultationDialog v-if="consultationVisible" :visible="consultationVisible" :params="detailParams" @close="consultationVisible = false"/>
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import dayjs from 'dayjs'
import { departmentRegister } from '@/utils/comprehensiveStatistics'
import { GetSelectedDept } from '@/utils/wartimeMode'
import ConsultationDialog from './detail.vue'
export default {
  components: {
    BgTitle,
    ConsultationDialog
  },
  data() {
    return {
      deptId: '',
      deptList: [

      ],
      date: '',
      tableLoading: false,
      tableData: [],
      page: 1,
      pagesTotal: 0,
      rolltimer: null,
      consultationVisible: false,
      detailParams: {},
      isLoadingMore: false
    }
  },
  mounted() {
    this.date = dayjs().format('YYYY-MM-DD')
    this.getUseDeptList()
    this.getDepartmentRegister()
  },
  destroyed () {
    clearInterval(this.rolltimer)
  },
  methods: {
    // 获取科室
    getUseDeptList() {
      GetSelectedDept().then((res) => {
        if (res.data.code == '200') {
          this.deptList = res.data.data
          this.deptList.unshift({
            deptName: '全部科室',
            id: ''
          })
        }
      })
    },
    // 时间类型切换
    deptChange(val) {
      this.deptId = val
      this.page = 1
      this.getDepartmentRegister()
    },
    dateChange (val) {
      this.date = val
      this.page = 1
      this.getDepartmentRegister()
    },
    showDetail () {
      this.consultationVisible = true
      this.detailParams = {
        date: this.date,
        data: {
          departmentId: this.deptId
        }
      }
    },
    getDepartmentRegister () {
      if (this.isLoadingMore) return
      
      let params = {
        departmentId: this.deptId,
        page: this.page,
        pageSize: 15,
        time: this.date
      }
      this.tableLoading = true
      this.isLoadingMore = true
      clearInterval(this.rolltimer)
      departmentRegister(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.pagesTotal = res.data.data.pages
          if (this.tableData && this.tableData.length > 8) {
            this.autoRoll() // 滚动
          } else {
            if (this.pagesTotal === this.page) {
              setTimeout(() => {
                this.page = 1
                this.getDepartmentRegister()
              }, 2000)
            }
          }
        }
      }).finally(() => {
        this.tableLoading = false
        this.isLoadingMore = false
      })
    },
    // 前置条件 1.table有height 2.show-header不能设置为false 值为false时不能正确活取到scrollHeight
    autoRoll (stop) {
      if (stop) {
        clearInterval(this.rolltimer)
        return
      }
      // 这里的 tab 是上方 table 表格绑定的ref值
      const table = this.$refs.table
      if (!table) return
      
      const divData = table.bodyWrapper
      this.rolltimer = setInterval(() => {
        if (this.isLoadingMore) return
        // + 4 是每秒向下滑动 4个像素  这块可以自己更改
        divData.scrollTop += 2
        // 下方判断是滑动到底部就会自己回到最上方重新开始滑动  改动像素的话 一定要满足进入这个判断  否则滚动到底部就停了
        if (divData.clientHeight + divData.scrollTop + 1 >= divData.scrollHeight && !this.isLoadingMore) {
          divData.scrollTop = 0
          clearInterval(this.rolltimer)
            
          setTimeout(() => {
            if (this.page < this.pagesTotal) {
              this.page++
              this.getDepartmentRegister()
            } else {
              this.page = 1
              this.getDepartmentRegister()
            }
          }, 300)
        }
      }, 100)
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.datePickerInput) {
  height: 34px;
  width: 150px;
  box-sizing: content-box;
  background: none;
  border: none;
  .el-input__inner {
    border: none;
  }
  .el-input__prefix {
    display: none;
  }
}
.bg-content {
  height: calc(100% - 34px);
  padding: 16px;
}
:deep(.el-table--striped) .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important; /* def2ff f2faff */
}
:deep(.el-table) {
  .el-table__body-wrapper::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: transparent;
  }
}
</style>
