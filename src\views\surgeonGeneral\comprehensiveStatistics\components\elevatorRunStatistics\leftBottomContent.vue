<template>
  <div class="statistics_item">
    <div class="item_title">停靠楼层占比</div>
    <div id="treeMap" ref="treemap"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { adsLiftFloorCountList } from '@/utils/comprehensiveStatistics'
export default {
  props: {
    elevatorId: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: 'day'
    }
  },
  data () {
    return {
      myChart: null
    }
  },
  watch: {
    elevatorId (val) {
      if (val) {

        this.getData()
      }
    },
    date (val) {
      if (val) {

        this.getData()
      }
    }
  },
  methods: {
    getData() {
      let params = {
        monitorDeviceId: this.elevatorId,
        dateType: this.date
      }
      adsLiftFloorCountList(params).then((res) => {
        if (res.data.code == 200) {
          let count = res.data.result.reduce((acc, cur) => {
            return acc + cur.floorCountSum
          }, 0)
          res.data.result.forEach((el) => {
            el.rate = ((el.floorCountSum / count) * 100).toFixed(2) + '%'
            el.name = el.floorName,
            el.value = el.floorCountSum
          })
          this.initTreeMap(res.data.result)
        }
      })
    },
    initTreeMap (data) {
      let chartDom = document.getElementById('treeMap')
      if (this.myChart) {
        this.myChart.dispose()
      }
      this.myChart = echarts.init(chartDom)
      let option = {}
      this.myChart.showLoading()
      if (data && data.length) {
        option = {
          tooltip: {
            show: true,
            formatter: function (params) {
              return params.name + ' : ' + params.value
            }
          },
          grid: {
            left: '5px', // 调整左侧边距
            right: '5px', // 调整右侧边距
            top: '30px', // 调整顶部边距，为图例留出空间
            bottom: '5px', // 调整底部边距
            containLabel: true
          },
          legend: { show: false },
          dataZoom: [
            {
              type: 'inside',
              zoomLock: true
            }
          ],
          series: [
            {
              type: 'treemap',
              width: '100%',
              height: '100%',
              breadcrumb: { show: false },
              visibleMin: 300,
              nodeClick: false,
              itemStyle: {
                normal: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 13
                  },
                  borderWidth: 1,
                  borderColor: 'rgba(0,0,0,0)'
                },
                emphasis: {
                  label: {
                    show: true
                  }
                }
              },
              label: {
                show: true,
                formatter: function (params) {
                  return `${params.name}\n${params.data.rate}`
                }
              },
              zoom: false,
              data: data
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      this.myChart.setOption(option)
      this.myChart.off('click')
      // 图例点击事件
      this.myChart.on('click', (params) => {
        console.log(params.data)
        this.$emit('itemClick', params.data)
      })
      this.myChart.hideLoading()
      // 禁用鼠标滚轮缩放
      this.myChart.getZr().off('mousewheel')

      // 禁用双击缩放
      this.myChart.getZr().off('dblclick')
    }
  }
}
</script>
<style lang="scss" scoped>
#treeMap {
  width: 100%;
  height: calc(100% - 26px);
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
