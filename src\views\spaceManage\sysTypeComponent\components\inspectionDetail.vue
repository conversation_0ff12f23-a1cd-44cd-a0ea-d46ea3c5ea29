<template>
  <el-dialog v-dialogDrag :modal="false" :visible.sync="hiddenDangerDetailsListShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="closed">
    <template slot="title">
      <span class="dialog-title">{{ systemType == 'xjrw' ? '巡检' : '保养' }}详情</span>
    </template>
    <div class="form-detail">
      <div class="plan-content">
        <ul class="item-row">
          <li class="width33">
            <span class="li-first-span">任务名称</span><span class="li-last-span">{{ dataInfo.taskName }}</span>
          </li>
          <li class="width33">
            <span class="li-first-span">周期类型</span><span class="li-last-span">{{ dataInfo.cycleType | filterList }}</span>
          </li>
          <li class="width33">
            <span class="li-first-span">应{{ systemType == 'xjrw' ? '巡检' : '保养' }}日期</span><span class="li-last-span">{{ moment(dataInfo.taskStartTime).format('YYYY-MM-DD') }}</span>
          </li>
        </ul>
        <ul class="item-row">
          <li class="width33">
            <span class="li-first-span">应{{ systemType == 'xjrw' ? '巡检' : '保养' }}时间</span><span class="li-last-span">{{ moment(dataInfo.taskStartTime).format('HH:mm:ss') + '-' + moment(dataInfo.taskEndTime).format('HH:mm:ss') }}</span>
          </li>
          <li class="width33">
            <span class="li-first-span">{{ systemType == 'xjrw' ? '巡检' : '保养' }}部门</span><span class="li-last-span">{{ dataInfo.distributionTeamName }}</span>
          </li>
          <li class="width33">
            <span class="li-first-span">{{ systemType == 'xjrw' ? '巡检' : '保养' }}人员</span><span class="li-last-span">{{ dataInfo.planPersonName || dataInfo.distributionTeamName }}</span>
          </li>
        </ul>

        <ul class="item-row">
          <li class="width33">
            <span class="li-first-span">实际{{ systemType == 'xjrw' ? '巡检' : '保养' }}时间</span>
            <span v-if="tableData.length > 1" class="li-last-span">{{ (dataInfo.executeStartTime ?? '') + ' - ' + (dataInfo.executeEndTime ?? '') }}</span>
            <span v-else class="li-last-span">{{ dataInfo.executeStartTime || '' }}</span>
          </li>
          <li class="width33">
            <span class="li-first-span">完成状态</span><span class="li-last-span">{{ dataInfo.taskStatus == '1' ? '未完成' : '已完成' }}</span>
          </li>
        </ul>
        <p style="padding: 1rem"></p>
        <!-- <el-table
          :data="tableData"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
        >
          <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
          <el-table-column fixed prop="alarmSource" show-overflow-tooltip label="报警来源"></el-table-column>
          <el-table-column fixed prop="alarmTypeName" show-overflow-tooltip label="报警类型"></el-table-column>
          <el-table-column fixed prop="deviceName" show-overflow-tooltip label="报警项名称"></el-table-column>
        </el-table> -->
        <!-- 模版1 -->
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
        >
          <el-table-column type="index" width="70" label="序号"></el-table-column>
          <el-table-column prop="taskPointName" show-overflow-tooltip :label="systemType == 'xjrw'?'巡检点名称':'保养点名称'" align="center" />
          <el-table-column prop="assetsRemarks" show-overflow-tooltip label="备注说明" align="center" />
          <el-table-column v-if="systemType == 'xjrw'" show-overflow-tooltip label="巡检结果" align="center">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.state == '3' || scope.row.state == '4' ? 'red' : '' }">{{
                scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : scope.row.state == '4' ? '异常报修' : '未巡检'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="systemType == 'byrw'" show-overflow-tooltip label=" 保养结果" align="center">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.state == '3' || scope.row.state == '4' ? 'red' : '' }">{{
                scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : scope.row.state == '4' ? '异常报修' : '未保养'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="guaranteeCode" show-overflow-tooltip label="工单编号" align="center"></el-table-column>
          <el-table-column prop="excuteTime" show-overflow-tooltip :label="systemType == 'xjrw' ? '实际巡检时间' : '实际保养时间'" align="center"></el-table-column>
          <el-table-column prop="implementPersonName" show-overflow-tooltip label="执行人员" align="center"></el-table-column>
          <el-table-column prop="spyScan" show-overflow-tooltip label="定位状态" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="操作" align="center">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="viewDetails(scope.row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <taskPointDetail ref="taskPointDetail" :taskType="taskType" :taskMap="taskMap" :systemType="systemType"></taskPointDetail>
  </el-dialog>
</template>

<script>
import moment from 'moment'
moment.locale('zh-cn')
import { GetInspTaskPointReleaseList, GetTaskPointReleaseList } from '@/utils/spaceManage'
import taskPointDetail from './taskPointDetail.vue'
export default {
  name: 'inspectionDetail',
  components: {
    taskPointDetail
  },
  filters: {
    filterList: function (value) {
      const option = [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ]
      return option.find((item) => item.cycleType === value)?.label ?? ''
    }
  },
  props: {
    dataInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    systemType: {
      type: String,
      default: 'xjrw'
    },
    taskType: {
      type: String,
      default: 'insp'
    }
  },
  data() {
    return {
      moment,
      detailId: '',
      ipasPointDetailShow: false,
      hiddenDangerDetailsListShow: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      taskMap: {}
    }
  },
  computed: {},
  mounted() {
    this.$tools.showDialog()
  },
  destroyed() {
    this.$tools.showDialog()
  },
  beforeDestroy() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    } catch (error) {}
    this.$emit('destroy')
  },
  created() {
    this.GetTaskPointReleaseList()
  },
  methods: {
    // 查看详情
    viewDetails(row) {
      this.$refs.taskPointDetail.hiddenDangerDetailsListShow = true
      this.$refs.taskPointDetail.getDataDetail(row.id)
      // this.$refs.taskPointDetail.getDataDetail('1899290398766510081')
    },
    GetTaskPointReleaseList(id, type = 'insp') {
      if (!id) return
      const codeList = [
        {
          type: 'insp',
          code: GetInspTaskPointReleaseList
        },
        {
          type: 'ipsm',
          code: GetTaskPointReleaseList
        }
      ]
      const code = codeList.find((e) => e.type == type).code
      code({ taskId: id, pageNo: this.currentPage, pageSize: this.pageSize }).then((res) => {
        if (res.data.code === '200') {
          const data = res.data.data
          this.total = parseInt(data.sum)
          this.tableData = data.list
          this.taskMap = data.taskMap || {}
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.GetTaskPointReleaseList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.GetTaskPointReleaseList()
    },
    closed() {
      this.hiddenDangerDetailsListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  overflow-y: scroll;
  box-sizing: border-box;
  .plan-content {
    width: 100%;
    // padding: 20px 0px 20px 20px;
    color: #b5bacb;
    font-size: 13px;
    .item-row {
      width: 100%;
      display: flex;
      padding: 12px 0px 20px 30px;
      box-sizing: border-box;
      .width33 {
        width: 33%;
        display: flex;
        align-items: center;
      }
      .width95 {
        width: 95%;
        display: flex;
      }
      ::v-deep .el-image__error,
      ::v-deep .el-image__placeholder {
        background: center;
      }
      .li-first-span {
        display: inline-block;
        width: 100px;
        // margin-right: 20px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #b0e3fa;
      }
      .li-last-span {
        display: inline-block;
        width: calc(100% - 120px);
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        // align-items: center;
      }
      #audio-box {
        display: flex;
      }
      #audio-box > audio {
        width: 260px;
        height: 30px;
      }
      #audio-box > a {
        width: 40px;
        text-align: center;
        background-color: #2cc7c5;
        height: 35px;
        line-height: 35px;
        color: #fff;
        border-radius: 5px;
        margin-left: 10px;
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background-color: transparent !important;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  pointer-events: auto;
  box-shadow: none;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
::v-deep .el-table {
  border: none !important;
  .el-table__header-wrapper {
    .cell {
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      white-space: nowrap;
    }
  }
  .el-table__body-wrapper {
    td.el-table__cell div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .el-table__body {
    tr {
      background: center;
    }
    td.el-table__cell,
    th.el-table__cell.is-leaf {
      border-right: 2px solid #0a164e;
      border-bottom: 2px solid #0a164e;
      background: rgba(56, 103, 180, 0.2);
      color: #fff;
    }
    .el-table__row:nth-child(2n - 1) {
      background: rgba(168, 172, 171, 0.08);
    }
    .el-table__row:hover {
      border: 0;
      opacity: 1;
      cursor: pointer;

      td div {
        color: rgba(255, 202, 100, 1);
      }
    }
  }
}
</style>
